<?php
/**
 * إدارة الأزرار التفاعلية
 * Interactive Buttons Management
 */

session_start();
require_once '../config/config.php';
require_once '../classes/WhatsAppAPI.php';
require_once '../classes/InteractiveButtons.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

$whatsappAPI = new WhatsAppAPI($database);
$interactiveButtons = new InteractiveButtons($database, $whatsappAPI);

$message = '';
$error = '';

// معالجة إرسال رسالة تفاعلية
if ($_POST && isset($_POST['action'])) {
    $phoneNumber = trim($_POST['phone_number'] ?? '');
    $action = $_POST['action'];
    
    if (empty($phoneNumber)) {
        $error = 'يرجى إدخال رقم الهاتف';
    } else {
        $phoneNumber = $whatsappAPI->validatePhoneNumber($phoneNumber);
        
        try {
            switch ($action) {
                case 'main_menu':
                    $result = $interactiveButtons->sendMainMenu($phoneNumber);
                    break;
                case 'services_menu':
                    $result = $interactiveButtons->sendServicesMenu($phoneNumber);
                    break;
                case 'support_menu':
                    $result = $interactiveButtons->sendSupportMenu($phoneNumber);
                    break;
                case 'rating_buttons':
                    $result = $interactiveButtons->sendRatingButtons($phoneNumber);
                    break;
                case 'contact_options':
                    $result = $interactiveButtons->sendContactOptions($phoneNumber);
                    break;
                case 'custom_buttons':
                    $result = $this->sendCustomButtons($phoneNumber, $_POST);
                    break;
            }
            
            if ($result) {
                $message = 'تم إرسال الرسالة التفاعلية بنجاح!';
            } else {
                $error = 'فشل في إرسال الرسالة';
            }
        } catch (Exception $e) {
            $error = 'خطأ: ' . $e->getMessage();
        }
    }
}

// جلب إحصائيات التفاعل
$buttonStats = $database->fetchAll("
    SELECT 
        button_id,
        button_title,
        COUNT(*) as click_count,
        DATE(created_at) as date
    FROM button_interactions 
    WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
    GROUP BY button_id, button_title, DATE(created_at)
    ORDER BY click_count DESC
    LIMIT 20
");

// جلب آخر التفاعلات
$recentInteractions = $database->fetchAll("
    SELECT 
        bi.*,
        c.name,
        c.phone_number
    FROM button_interactions bi
    JOIN contacts c ON bi.contact_id = c.id
    ORDER BY bi.created_at DESC
    LIMIT 50
");

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الأزرار التفاعلية - نظام أتمتة WhatsApp Business</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/admin.css" rel="stylesheet">
</head>
<body>
    <?php include 'includes/navbar.php'; ?>
    
    <div class="container-fluid">
        <div class="row">
            <?php include 'includes/sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">الأزرار التفاعلية</h1>
                </div>

                <?php if ($message): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle"></i>
                        <?php echo htmlspecialchars($message); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if ($error): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-triangle"></i>
                        <?php echo htmlspecialchars($error); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- إرسال رسائل تفاعلية -->
                <div class="row mb-4">
                    <div class="col-lg-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">إرسال رسالة تفاعلية</h5>
                            </div>
                            <div class="card-body">
                                <form method="POST">
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="phone_number" class="form-label">رقم الهاتف</label>
                                            <input type="text" class="form-control" id="phone_number" name="phone_number" 
                                                   placeholder="+966501234567" required>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="action" class="form-label">نوع الرسالة</label>
                                            <select class="form-select" id="action" name="action" required>
                                                <option value="">اختر نوع الرسالة</option>
                                                <option value="main_menu">القائمة الرئيسية</option>
                                                <option value="services_menu">قائمة الخدمات</option>
                                                <option value="support_menu">قائمة الدعم</option>
                                                <option value="rating_buttons">أزرار التقييم</option>
                                                <option value="contact_options">خيارات التواصل</option>
                                            </select>
                                        </div>
                                    </div>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-paper-plane"></i> إرسال
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">معاينة الأزرار</h6>
                            </div>
                            <div class="card-body">
                                <div class="button-preview">
                                    <div class="whatsapp-message">
                                        <div class="message-header">القائمة الرئيسية</div>
                                        <div class="message-body">مرحباً بك! كيف يمكنني مساعدتك اليوم؟</div>
                                        <div class="interactive-buttons">
                                            <button class="btn btn-outline-primary btn-sm mb-1">🛍️ خدماتنا</button>
                                            <button class="btn btn-outline-primary btn-sm mb-1">🆘 الدعم الفني</button>
                                            <button class="btn btn-outline-primary btn-sm mb-1">📞 تواصل معنا</button>
                                        </div>
                                        <div class="message-footer">اختر أحد الخيارات أدناه</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- إحصائيات التفاعل -->
                <div class="row mb-4">
                    <div class="col-lg-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">أكثر الأزرار استخداماً (آخر 7 أيام)</h6>
                            </div>
                            <div class="card-body">
                                <canvas id="buttonStatsChart"></canvas>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">إحصائيات سريعة</h6>
                            </div>
                            <div class="card-body">
                                <?php
                                $totalInteractions = $database->fetchOne("SELECT COUNT(*) as count FROM button_interactions")['count'];
                                $todayInteractions = $database->fetchOne("SELECT COUNT(*) as count FROM button_interactions WHERE DATE(created_at) = CURDATE()")['count'];
                                $uniqueUsers = $database->fetchOne("SELECT COUNT(DISTINCT contact_id) as count FROM button_interactions WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)")['count'];
                                ?>
                                <div class="row text-center">
                                    <div class="col-4">
                                        <h4 class="text-primary"><?php echo number_format($totalInteractions); ?></h4>
                                        <small>إجمالي التفاعلات</small>
                                    </div>
                                    <div class="col-4">
                                        <h4 class="text-success"><?php echo number_format($todayInteractions); ?></h4>
                                        <small>تفاعلات اليوم</small>
                                    </div>
                                    <div class="col-4">
                                        <h4 class="text-info"><?php echo number_format($uniqueUsers); ?></h4>
                                        <small>مستخدمين نشطين</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- آخر التفاعلات -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">آخر التفاعلات</h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>العميل</th>
                                        <th>رقم الهاتف</th>
                                        <th>الزر</th>
                                        <th>النوع</th>
                                        <th>التاريخ</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recentInteractions as $interaction): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($interaction['name'] ?? 'غير محدد'); ?></td>
                                        <td><?php echo htmlspecialchars($interaction['phone_number']); ?></td>
                                        <td>
                                            <span class="badge bg-primary">
                                                <?php echo htmlspecialchars($interaction['button_title'] ?? $interaction['button_id']); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?php echo $interaction['interaction_type'] === 'button_click' ? 'success' : 'info'; ?>">
                                                <?php echo $interaction['interaction_type'] === 'button_click' ? 'نقر زر' : 'اختيار قائمة'; ?>
                                            </span>
                                        </td>
                                        <td><?php echo date('Y-m-d H:i', strtotime($interaction['created_at'])); ?></td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // رسم بياني لإحصائيات الأزرار
        const buttonStatsData = <?php echo json_encode($buttonStats); ?>;
        
        if (buttonStatsData.length > 0) {
            const labels = buttonStatsData.map(item => item.button_title || item.button_id);
            const data = buttonStatsData.map(item => item.click_count);
            
            new Chart(document.getElementById('buttonStatsChart'), {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'عدد النقرات',
                        data: data,
                        backgroundColor: '#007bff',
                        borderColor: '#0056b3',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }
    </script>

    <style>
        .button-preview {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
        }
        
        .whatsapp-message {
            background: white;
            border-radius: 10px;
            padding: 15px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .message-header {
            font-weight: bold;
            color: #25D366;
            margin-bottom: 10px;
        }
        
        .message-body {
            margin-bottom: 15px;
            color: #333;
        }
        
        .interactive-buttons {
            margin-bottom: 10px;
        }
        
        .interactive-buttons .btn {
            display: block;
            width: 100%;
            text-align: right;
        }
        
        .message-footer {
            font-size: 0.9em;
            color: #666;
        }
    </style>
</body>
</html>
