const winston = require('winston');
const path = require('path');
const fs = require('fs-extra');
const config = require('../config/config');

// Ensure logs directory exists
fs.ensureDirSync(path.dirname(config.logging.file));

// Custom format for Arabic support
const arabicFormat = winston.format.combine(
  winston.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss'
  }),
  winston.format.errors({ stack: true }),
  winston.format.printf(({ level, message, timestamp, stack }) => {
    return `${timestamp} [${level.toUpperCase()}]: ${stack || message}`;
  })
);

// Create logger instance
const logger = winston.createLogger({
  level: config.logging.level,
  format: arabicFormat,
  transports: [
    // File transport
    new winston.transports.File({
      filename: config.logging.file,
      maxsize: 5242880, // 5MB
      maxFiles: 5,
      tailable: true
    }),
    
    // Error file transport
    new winston.transports.File({
      filename: path.join(path.dirname(config.logging.file), 'error.log'),
      level: 'error',
      maxsize: 5242880, // 5MB
      maxFiles: 3
    })
  ]
});

// Add console transport in development
if (config.NODE_ENV !== 'production') {
  logger.add(new winston.transports.Console({
    format: winston.format.combine(
      winston.format.colorize(),
      winston.format.simple()
    )
  }));
}

// Helper methods
logger.whatsapp = (message, data = {}) => {
  logger.info(`[WhatsApp] ${message}`, data);
};

logger.automation = (message, data = {}) => {
  logger.info(`[Automation] ${message}`, data);
};

logger.scheduler = (message, data = {}) => {
  logger.info(`[Scheduler] ${message}`, data);
};

logger.contact = (message, data = {}) => {
  logger.info(`[Contact] ${message}`, data);
};

module.exports = logger;
