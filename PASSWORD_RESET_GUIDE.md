# 🔐 دليل إعادة تعيين كلمة المرور
# Password Reset Guide

## 🚨 مشكلة: "الرقم السري خطأ"

إذا كنت تواجه مشكلة في تسجيل الدخول بالبيانات الافتراضية، إليك الحلول:

## 🛠️ الحلول المتاحة

### 1. **الحل السريع - عبر المتصفح** ⭐ (الأسهل)
```
افتح الرابط التالي في المتصفح:
http://yourdomain.com/admin/reset_password.php?key=reset_admin_2024
```

### 2. **الحل عبر سطر الأوامر**
```bash
# انتقل إلى مجلد المشروع
cd /path/to/whatsapp-automation

# شغل سكريبت إعادة التعيين
php reset_admin_password.php
```

### 3. **الحل اليدوي - قاعدة البيانات**
```sql
-- الاتصال بـ MySQL
mysql -u root -p

-- اختيار قاعدة البيانات
USE whatsapp_automation;

-- إعادة تعيين كلمة المرور (admin123)
UPDATE users 
SET password_hash = '$2y$10$TKh8H1.PfQx37YgCzwiKb.KjNyWgaHb9cbcoQgdIVFlYg7B77UdFm' 
WHERE username = 'admin';
```

### 4. **إنشاء مستخدم جديد**
```sql
-- حذف المستخدم القديم (اختياري)
DELETE FROM users WHERE username = 'admin';

-- إنشاء مستخدم جديد
INSERT INTO users (username, email, password_hash, full_name, role, is_active) 
VALUES ('admin', '<EMAIL>', '$2y$10$TKh8H1.PfQx37YgCzwiKb.KjNyWgaHb9cbcoQgdIVFlYg7B77UdFm', 'المدير العام', 'admin', 1);
```

## 📋 البيانات الصحيحة بعد الإصلاح

```
اسم المستخدم: admin
كلمة المرور: admin123
البريد الإلكتروني: <EMAIL>
```

## 🔍 التحقق من نجاح الإصلاح

### اختبار سريع بـ PHP:
```php
<?php
require_once 'config/config.php';

$database = new Database();
$user = $database->fetchOne("SELECT * FROM users WHERE username = 'admin'");

if ($user && password_verify('admin123', $user['password_hash'])) {
    echo "✅ كلمة المرور صحيحة - يمكنك تسجيل الدخول\n";
} else {
    echo "❌ كلمة المرور خاطئة - جرب الحلول أعلاه\n";
}
?>
```

## 🚨 أسباب المشكلة الشائعة

1. **Hash خاطئ في قاعدة البيانات**
   - الحل: استخدم أحد الحلول أعلاه

2. **قاعدة البيانات فارغة**
   - الحل: شغل ملف `database/whatsapp_automation.sql`

3. **إعدادات قاعدة البيانات خاطئة**
   - تحقق من ملف `.env`
   - تأكد من صحة بيانات الاتصال

4. **مشكلة في PHP password_verify**
   - تأكد من إصدار PHP 7.0+
   - تحقق من إعدادات PHP

## 🔧 خطوات التشخيص

### 1. تحقق من قاعدة البيانات:
```bash
mysql -u root -p -e "USE whatsapp_automation; SELECT username, email FROM users WHERE username = 'admin';"
```

### 2. تحقق من ملف الإعدادات:
```bash
cat .env | grep DB_
```

### 3. تحقق من إصدار PHP:
```bash
php -v
php -m | grep -E "(pdo_mysql|password)"
```

## ⚠️ ملاحظات أمنية

1. **احذف ملف reset_password.php** بعد حل المشكلة
2. **غيّر كلمة المرور** بعد تسجيل الدخول
3. **استخدم كلمة مرور قوية** في الإنتاج
4. **فعّل HTTPS** للحماية

## 📞 الدعم الإضافي

إذا لم تنجح الحلول أعلاه:

1. تحقق من سجلات الأخطاء في `logs/`
2. شغل `quick_start.php` للتشخيص
3. تأكد من صحة إعدادات قاعدة البيانات
4. جرب إعادة تثبيت النظام

## 🎯 الحل السريع (نسخ ولصق)

```bash
# الحل الأسرع - شغل هذا الأمر:
php -r "
require_once 'config/config.php';
\$db = new Database();
\$hash = password_hash('admin123', PASSWORD_DEFAULT);
\$updated = \$db->update('users', ['password_hash' => \$hash], 'username = ?', ['admin']);
echo \$updated ? '✅ تم إصلاح كلمة المرور' : '❌ فشل الإصلاح';
"
```

---

**تذكر:** البيانات الافتراضية هي `admin` / `admin123` - استخدمها بعد تطبيق أي من الحلول أعلاه.
