<?php
/**
 * فئة WhatsApp Business API
 * WhatsApp Business API Class
 */

class WhatsAppAPI {
    private $accessToken;
    private $phoneNumberId;
    private $apiUrl;
    private $database;
    
    public function __construct($database) {
        $this->accessToken = WHATSAPP_ACCESS_TOKEN;
        $this->phoneNumberId = WHATSAPP_PHONE_NUMBER_ID;
        $this->apiUrl = WHATSAPP_API_URL;
        $this->database = $database;
    }
    
    /**
     * إرسال رسالة نصية
     * Send text message
     */
    public function sendTextMessage($to, $message, $isAutomated = false) {
        $data = [
            'messaging_product' => 'whatsapp',
            'to' => $to,
            'type' => 'text',
            'text' => [
                'body' => $message
            ]
        ];
        
        $response = $this->makeRequest('messages', $data);
        
        if ($response && isset($response['messages'][0]['id'])) {
            $this->logMessage($to, 'outgoing', 'text', $message, $response['messages'][0]['id'], $isAutomated);
            return $response['messages'][0]['id'];
        }
        
        return false;
    }
    
    /**
     * إرسال رسالة بقالب
     * Send template message
     */
    public function sendTemplateMessage($to, $templateName, $parameters = []) {
        $data = [
            'messaging_product' => 'whatsapp',
            'to' => $to,
            'type' => 'template',
            'template' => [
                'name' => $templateName,
                'language' => [
                    'code' => 'ar'
                ]
            ]
        ];
        
        if (!empty($parameters)) {
            $data['template']['components'] = [
                [
                    'type' => 'body',
                    'parameters' => $parameters
                ]
            ];
        }
        
        $response = $this->makeRequest('messages', $data);
        
        if ($response && isset($response['messages'][0]['id'])) {
            $this->logMessage($to, 'outgoing', 'template', $templateName, $response['messages'][0]['id'], true);
            return $response['messages'][0]['id'];
        }
        
        return false;
    }
    
    /**
     * إرسال رسالة بوسائط
     * Send media message
     */
    public function sendMediaMessage($to, $mediaType, $mediaUrl, $caption = '') {
        $data = [
            'messaging_product' => 'whatsapp',
            'to' => $to,
            'type' => $mediaType,
            $mediaType => [
                'link' => $mediaUrl
            ]
        ];
        
        if ($caption && in_array($mediaType, ['image', 'video', 'document'])) {
            $data[$mediaType]['caption'] = $caption;
        }
        
        $response = $this->makeRequest('messages', $data);
        
        if ($response && isset($response['messages'][0]['id'])) {
            $this->logMessage($to, 'outgoing', $mediaType, $caption, $response['messages'][0]['id'], false, $mediaUrl);
            return $response['messages'][0]['id'];
        }
        
        return false;
    }
    
    /**
     * تحميل ملف وسائط
     * Upload media file
     */
    public function uploadMedia($filePath, $type) {
        $data = [
            'messaging_product' => 'whatsapp',
            'type' => $type
        ];
        
        $file = new CURLFile($filePath, mime_content_type($filePath), basename($filePath));
        $data['file'] = $file;
        
        $response = $this->makeRequest('media', $data, true);
        
        return $response['id'] ?? false;
    }
    
    /**
     * الحصول على معلومات الوسائط
     * Get media info
     */
    public function getMediaInfo($mediaId) {
        return $this->makeRequest($mediaId, [], false, 'GET');
    }
    
    /**
     * تحديث حالة الرسالة
     * Update message status
     */
    public function markMessageAsRead($messageId) {
        $data = [
            'messaging_product' => 'whatsapp',
            'status' => 'read',
            'message_id' => $messageId
        ];
        
        return $this->makeRequest('messages', $data);
    }
    
    /**
     * إجراء طلب API
     * Make API request
     */
    private function makeRequest($endpoint, $data = [], $isUpload = false, $method = 'POST') {
        $url = $this->apiUrl . $this->phoneNumberId . '/' . $endpoint;
        
        $headers = [
            'Authorization: Bearer ' . $this->accessToken
        ];
        
        if (!$isUpload) {
            $headers[] = 'Content-Type: application/json';
        }
        
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_CUSTOMREQUEST => $method,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_SSL_VERIFYPEER => false
        ]);
        
        if ($method === 'POST' && !empty($data)) {
            if ($isUpload) {
                curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
            } else {
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            }
        }
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            $this->logError("cURL Error: " . $error);
            return false;
        }
        
        $decodedResponse = json_decode($response, true);
        
        if ($httpCode >= 400) {
            $this->logError("API Error: " . $response);
            return false;
        }
        
        return $decodedResponse;
    }
    
    /**
     * تسجيل الرسالة في قاعدة البيانات
     * Log message to database
     */
    private function logMessage($phoneNumber, $direction, $type, $content, $messageId = null, $isAutomated = false, $mediaUrl = null) {
        try {
            // البحث عن جهة الاتصال أو إنشاؤها
            $contact = $this->database->fetchOne(
                "SELECT id FROM contacts WHERE phone_number = ?",
                [$phoneNumber]
            );
            
            if (!$contact) {
                $contactId = $this->database->insert('contacts', [
                    'phone_number' => $phoneNumber,
                    'created_at' => date('Y-m-d H:i:s')
                ]);
            } else {
                $contactId = $contact['id'];
                // تحديث آخر رسالة
                $this->database->update(
                    'contacts',
                    ['last_message_at' => date('Y-m-d H:i:s')],
                    'id = ?',
                    [$contactId]
                );
            }
            
            // تسجيل الرسالة
            $messageData = [
                'message_id' => $messageId,
                'contact_id' => $contactId,
                'direction' => $direction,
                'message_type' => $type,
                'content' => $content,
                'media_url' => $mediaUrl,
                'status' => 'sent',
                'is_automated' => $isAutomated ? 1 : 0,
                'sent_at' => date('Y-m-d H:i:s'),
                'created_at' => date('Y-m-d H:i:s')
            ];
            
            return $this->database->insert('messages', $messageData);
            
        } catch (Exception $e) {
            $this->logError("Error logging message: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * تسجيل الأخطاء
     * Log errors
     */
    private function logError($message) {
        $logFile = LOG_PATH . 'whatsapp_api_' . date('Y-m-d') . '.log';
        $timestamp = date('Y-m-d H:i:s');
        file_put_contents($logFile, "[$timestamp] ERROR: $message\n", FILE_APPEND | LOCK_EX);
    }
    
    /**
     * التحقق من صحة رقم الهاتف
     * Validate phone number
     */
    public function validatePhoneNumber($phoneNumber) {
        // إزالة الرموز غير المرغوب فيها
        $phoneNumber = preg_replace('/[^0-9+]/', '', $phoneNumber);
        
        // التحقق من الطول والتنسيق
        if (strlen($phoneNumber) < 10 || strlen($phoneNumber) > 15) {
            return false;
        }
        
        // إضافة رمز الدولة إذا لم يكن موجوداً
        if (!str_starts_with($phoneNumber, '+')) {
            if (str_starts_with($phoneNumber, '966')) {
                $phoneNumber = '+' . $phoneNumber;
            } elseif (str_starts_with($phoneNumber, '05')) {
                $phoneNumber = '+966' . substr($phoneNumber, 1);
            } else {
                $phoneNumber = '+966' . $phoneNumber;
            }
        }
        
        return $phoneNumber;
    }
    
    /**
     * الحصول على إحصائيات الرسائل
     * Get message statistics
     */
    public function getMessageStats($startDate = null, $endDate = null) {
        $whereClause = "1=1";
        $params = [];
        
        if ($startDate) {
            $whereClause .= " AND created_at >= ?";
            $params[] = $startDate;
        }
        
        if ($endDate) {
            $whereClause .= " AND created_at <= ?";
            $params[] = $endDate . ' 23:59:59';
        }
        
        $stats = $this->database->fetchOne("
            SELECT 
                COUNT(*) as total_messages,
                SUM(CASE WHEN direction = 'outgoing' THEN 1 ELSE 0 END) as sent_messages,
                SUM(CASE WHEN direction = 'incoming' THEN 1 ELSE 0 END) as received_messages,
                SUM(CASE WHEN is_automated = 1 THEN 1 ELSE 0 END) as automated_messages,
                SUM(CASE WHEN status = 'delivered' THEN 1 ELSE 0 END) as delivered_messages,
                SUM(CASE WHEN status = 'read' THEN 1 ELSE 0 END) as read_messages
            FROM messages 
            WHERE {$whereClause}
        ", $params);
        
        return $stats;
    }
}
?>
