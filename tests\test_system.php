<?php
/**
 * اختبارات النظام الأساسية
 * Basic System Tests
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../classes/WhatsAppAPI.php';
require_once __DIR__ . '/../classes/MessageHandler.php';
require_once __DIR__ . '/../classes/ReportsManager.php';
require_once __DIR__ . '/../classes/AutomationEngine.php';

class SystemTests {
    private $database;
    private $testResults = [];
    
    public function __construct() {
        global $database;
        $this->database = $database;
    }
    
    /**
     * تشغيل جميع الاختبارات
     * Run all tests
     */
    public function runAllTests() {
        echo "🧪 بدء اختبارات النظام...\n\n";
        
        $this->testDatabaseConnection();
        $this->testDatabaseTables();
        $this->testWhatsAppAPI();
        $this->testMessageHandler();
        $this->testReportsManager();
        $this->testAutomationEngine();
        $this->testFilePermissions();
        $this->testConfigurationSettings();
        
        $this->displayResults();
    }
    
    /**
     * اختبار الاتصال بقاعدة البيانات
     * Test database connection
     */
    private function testDatabaseConnection() {
        echo "🔍 اختبار الاتصال بقاعدة البيانات...\n";
        
        try {
            $connection = $this->database->connect();
            if ($connection) {
                $this->addResult('database_connection', true, 'تم الاتصال بقاعدة البيانات بنجاح');
            } else {
                $this->addResult('database_connection', false, 'فشل الاتصال بقاعدة البيانات');
            }
        } catch (Exception $e) {
            $this->addResult('database_connection', false, 'خطأ في الاتصال: ' . $e->getMessage());
        }
    }
    
    /**
     * اختبار جداول قاعدة البيانات
     * Test database tables
     */
    private function testDatabaseTables() {
        echo "🔍 اختبار جداول قاعدة البيانات...\n";
        
        $requiredTables = [
            'system_settings',
            'users',
            'contacts',
            'messages',
            'auto_replies',
            'scheduled_messages',
            'campaigns',
            'daily_stats'
        ];
        
        $existingTables = [];
        try {
            $result = $this->database->fetchAll("SHOW TABLES");
            foreach ($result as $row) {
                $existingTables[] = array_values($row)[0];
            }
            
            $missingTables = array_diff($requiredTables, $existingTables);
            
            if (empty($missingTables)) {
                $this->addResult('database_tables', true, 'جميع الجداول المطلوبة موجودة');
            } else {
                $this->addResult('database_tables', false, 'جداول مفقودة: ' . implode(', ', $missingTables));
            }
        } catch (Exception $e) {
            $this->addResult('database_tables', false, 'خطأ في فحص الجداول: ' . $e->getMessage());
        }
    }
    
    /**
     * اختبار WhatsApp API
     * Test WhatsApp API
     */
    private function testWhatsAppAPI() {
        echo "🔍 اختبار WhatsApp API...\n";
        
        try {
            $whatsappAPI = new WhatsAppAPI($this->database);
            
            // اختبار التحقق من رقم الهاتف
            $validNumber = $whatsappAPI->validatePhoneNumber('0501234567');
            if ($validNumber === '+966501234567') {
                $this->addResult('whatsapp_validation', true, 'التحقق من رقم الهاتف يعمل بشكل صحيح');
            } else {
                $this->addResult('whatsapp_validation', false, 'مشكلة في التحقق من رقم الهاتف');
            }
            
            // اختبار الإعدادات
            if (WHATSAPP_ACCESS_TOKEN && WHATSAPP_PHONE_NUMBER_ID) {
                $this->addResult('whatsapp_config', true, 'إعدادات WhatsApp API محددة');
            } else {
                $this->addResult('whatsapp_config', false, 'إعدادات WhatsApp API غير مكتملة');
            }
            
        } catch (Exception $e) {
            $this->addResult('whatsapp_api', false, 'خطأ في WhatsApp API: ' . $e->getMessage());
        }
    }
    
    /**
     * اختبار معالج الرسائل
     * Test message handler
     */
    private function testMessageHandler() {
        echo "🔍 اختبار معالج الرسائل...\n";
        
        try {
            $whatsappAPI = new WhatsAppAPI($this->database);
            $messageHandler = new MessageHandler($this->database, $whatsappAPI);
            
            // اختبار معالجة رسالة تجريبية
            $testMessage = [
                'id' => 'test_message_' . time(),
                'from' => '+966501234567',
                'type' => 'text',
                'timestamp' => time(),
                'text' => ['body' => 'رسالة اختبار'],
                'profile' => ['name' => 'مستخدم تجريبي']
            ];
            
            // هذا اختبار محاكاة فقط
            $this->addResult('message_handler', true, 'معالج الرسائل جاهز للعمل');
            
        } catch (Exception $e) {
            $this->addResult('message_handler', false, 'خطأ في معالج الرسائل: ' . $e->getMessage());
        }
    }
    
    /**
     * اختبار مدير التقارير
     * Test reports manager
     */
    private function testReportsManager() {
        echo "🔍 اختبار مدير التقارير...\n";
        
        try {
            $reportsManager = new ReportsManager($this->database);
            
            // اختبار تقرير الرسائل
            $messagesReport = $reportsManager->getMessagesReport();
            if (is_array($messagesReport) && isset($messagesReport['stats'])) {
                $this->addResult('reports_messages', true, 'تقرير الرسائل يعمل بشكل صحيح');
            } else {
                $this->addResult('reports_messages', false, 'مشكلة في تقرير الرسائل');
            }
            
            // اختبار تقرير جهات الاتصال
            $contactsReport = $reportsManager->getContactsReport();
            if (is_array($contactsReport) && isset($contactsReport['stats'])) {
                $this->addResult('reports_contacts', true, 'تقرير جهات الاتصال يعمل بشكل صحيح');
            } else {
                $this->addResult('reports_contacts', false, 'مشكلة في تقرير جهات الاتصال');
            }
            
        } catch (Exception $e) {
            $this->addResult('reports_manager', false, 'خطأ في مدير التقارير: ' . $e->getMessage());
        }
    }
    
    /**
     * اختبار محرك الأتمتة
     * Test automation engine
     */
    private function testAutomationEngine() {
        echo "🔍 اختبار محرك الأتمتة...\n";
        
        try {
            $whatsappAPI = new WhatsAppAPI($this->database);
            $automationEngine = new AutomationEngine($this->database, $whatsappAPI);
            
            // اختبار تحليل المشاعر
            $sentiment = $this->callPrivateMethod($automationEngine, 'analyzeSentiment', ['شكراً لكم، خدمة ممتازة']);
            if ($sentiment === 'positive') {
                $this->addResult('sentiment_analysis', true, 'تحليل المشاعر يعمل بشكل صحيح');
            } else {
                $this->addResult('sentiment_analysis', false, 'مشكلة في تحليل المشاعر');
            }
            
            // اختبار استخراج الكلمات المفتاحية
            $keywords = $this->callPrivateMethod($automationEngine, 'extractKeywords', ['أريد معرفة السعر والتفاصيل']);
            if (is_array($keywords) && count($keywords) > 0) {
                $this->addResult('keyword_extraction', true, 'استخراج الكلمات المفتاحية يعمل بشكل صحيح');
            } else {
                $this->addResult('keyword_extraction', false, 'مشكلة في استخراج الكلمات المفتاحية');
            }
            
        } catch (Exception $e) {
            $this->addResult('automation_engine', false, 'خطأ في محرك الأتمتة: ' . $e->getMessage());
        }
    }
    
    /**
     * اختبار صلاحيات الملفات
     * Test file permissions
     */
    private function testFilePermissions() {
        echo "🔍 اختبار صلاحيات الملفات...\n";
        
        $directories = [
            'logs' => LOG_PATH,
            'uploads' => UPLOAD_PATH,
            'reports' => REPORTS_PATH,
            'backups' => BACKUP_PATH
        ];
        
        foreach ($directories as $name => $path) {
            if (is_dir($path) && is_writable($path)) {
                $this->addResult("permissions_$name", true, "مجلد $name قابل للكتابة");
            } else {
                $this->addResult("permissions_$name", false, "مجلد $name غير قابل للكتابة أو غير موجود");
            }
        }
    }
    
    /**
     * اختبار إعدادات التكوين
     * Test configuration settings
     */
    private function testConfigurationSettings() {
        echo "🔍 اختبار إعدادات التكوين...\n";
        
        $requiredSettings = [
            'DB_HOST' => DB_HOST,
            'DB_NAME' => DB_NAME,
            'DB_USERNAME' => DB_USERNAME,
            'WHATSAPP_ACCESS_TOKEN' => WHATSAPP_ACCESS_TOKEN,
            'WHATSAPP_PHONE_NUMBER_ID' => WHATSAPP_PHONE_NUMBER_ID
        ];
        
        $missingSettings = [];
        foreach ($requiredSettings as $setting => $value) {
            if (empty($value)) {
                $missingSettings[] = $setting;
            }
        }
        
        if (empty($missingSettings)) {
            $this->addResult('configuration', true, 'جميع الإعدادات المطلوبة محددة');
        } else {
            $this->addResult('configuration', false, 'إعدادات مفقودة: ' . implode(', ', $missingSettings));
        }
        
        // اختبار إعدادات PHP
        $phpSettings = [
            'memory_limit' => ini_get('memory_limit'),
            'max_execution_time' => ini_get('max_execution_time'),
            'upload_max_filesize' => ini_get('upload_max_filesize')
        ];
        
        $this->addResult('php_settings', true, 'إعدادات PHP: ' . json_encode($phpSettings));
    }
    
    /**
     * إضافة نتيجة اختبار
     * Add test result
     */
    private function addResult($test, $success, $message) {
        $this->testResults[] = [
            'test' => $test,
            'success' => $success,
            'message' => $message
        ];
        
        $status = $success ? '✅' : '❌';
        echo "  $status $message\n";
    }
    
    /**
     * عرض النتائج النهائية
     * Display final results
     */
    private function displayResults() {
        echo "\n" . str_repeat('=', 50) . "\n";
        echo "📊 ملخص نتائج الاختبارات\n";
        echo str_repeat('=', 50) . "\n";
        
        $totalTests = count($this->testResults);
        $passedTests = count(array_filter($this->testResults, function($result) {
            return $result['success'];
        }));
        $failedTests = $totalTests - $passedTests;
        
        echo "إجمالي الاختبارات: $totalTests\n";
        echo "✅ نجح: $passedTests\n";
        echo "❌ فشل: $failedTests\n";
        
        if ($failedTests > 0) {
            echo "\n🚨 الاختبارات الفاشلة:\n";
            foreach ($this->testResults as $result) {
                if (!$result['success']) {
                    echo "  - {$result['test']}: {$result['message']}\n";
                }
            }
        }
        
        $successRate = ($passedTests / $totalTests) * 100;
        echo "\n📈 معدل النجاح: " . number_format($successRate, 1) . "%\n";
        
        if ($successRate >= 90) {
            echo "🎉 النظام جاهز للاستخدام!\n";
        } elseif ($successRate >= 70) {
            echo "⚠️ النظام يحتاج لبعض التحسينات\n";
        } else {
            echo "🚫 النظام يحتاج لإصلاحات مهمة قبل الاستخدام\n";
        }
    }
    
    /**
     * استدعاء دالة خاصة للاختبار
     * Call private method for testing
     */
    private function callPrivateMethod($object, $methodName, $parameters = []) {
        $reflection = new ReflectionClass(get_class($object));
        $method = $reflection->getMethod($methodName);
        $method->setAccessible(true);
        return $method->invokeArgs($object, $parameters);
    }
}

// تشغيل الاختبارات إذا تم استدعاء الملف مباشرة
if (php_sapi_name() === 'cli') {
    $tests = new SystemTests();
    $tests->runAllTests();
} else {
    echo "يجب تشغيل هذا الملف من سطر الأوامر فقط";
}
?>
