<?php
/**
 * لوحة التحكم الرئيسية
 * Main Dashboard
 */

session_start();
require_once '../config/config.php';
require_once '../classes/WhatsAppAPI.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

// جلب الإحصائيات
$whatsappAPI = new WhatsAppAPI($database);

// إحصائيات اليوم
$todayStats = $whatsappAPI->getMessageStats(date('Y-m-d'));

// إحصائيات الأسبوع
$weekStart = date('Y-m-d', strtotime('-7 days'));
$weekStats = $whatsappAPI->getMessageStats($weekStart);

// إحصائيات الشهر
$monthStart = date('Y-m-01');
$monthStats = $whatsappAPI->getMessageStats($monthStart);

// جلب آخر الرسائل
$recentMessages = $database->fetchAll("
    SELECT m.*, c.name, c.phone_number 
    FROM messages m 
    JOIN contacts c ON m.contact_id = c.id 
    ORDER BY m.created_at DESC 
    LIMIT 10
");

// جلب جهات الاتصال النشطة
$activeContacts = $database->fetchOne("
    SELECT COUNT(*) as count 
    FROM contacts 
    WHERE last_message_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
")['count'];

// جلب الردود التلقائية النشطة
$activeAutoReplies = $database->fetchOne("
    SELECT COUNT(*) as count 
    FROM auto_replies 
    WHERE is_active = 1
")['count'];

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - نظام أتمتة WhatsApp Business</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/admin.css" rel="stylesheet">
</head>
<body>
    <?php include 'includes/navbar.php'; ?>
    
    <div class="container-fluid">
        <div class="row">
            <?php include 'includes/sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">لوحة التحكم</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary">تصدير</button>
                        </div>
                        <button type="button" class="btn btn-sm btn-primary">
                            <i class="fas fa-plus"></i> إضافة جديد
                        </button>
                    </div>
                </div>

                <!-- بطاقات الإحصائيات -->
                <div class="row mb-4">
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-right-primary shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                            رسائل اليوم
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            <?php echo number_format($todayStats['total_messages'] ?? 0); ?>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-envelope fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-right-success shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                            رسائل مرسلة
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            <?php echo number_format($todayStats['sent_messages'] ?? 0); ?>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-paper-plane fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-right-info shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                            جهات اتصال نشطة
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            <?php echo number_format($activeContacts); ?>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-users fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-right-warning shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                            ردود تلقائية
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            <?php echo number_format($activeAutoReplies); ?>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-robot fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الرسوم البيانية -->
                <div class="row mb-4">
                    <div class="col-lg-8">
                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">إحصائيات الرسائل - آخر 7 أيام</h6>
                            </div>
                            <div class="card-body">
                                <canvas id="messagesChart"></canvas>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-4">
                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">توزيع أنواع الرسائل</h6>
                            </div>
                            <div class="card-body">
                                <canvas id="messageTypesChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- آخر الرسائل -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">آخر الرسائل</h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered" width="100%" cellspacing="0">
                                <thead>
                                    <tr>
                                        <th>جهة الاتصال</th>
                                        <th>رقم الهاتف</th>
                                        <th>نوع الرسالة</th>
                                        <th>المحتوى</th>
                                        <th>الاتجاه</th>
                                        <th>الحالة</th>
                                        <th>التاريخ</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recentMessages as $message): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($message['name'] ?? 'غير محدد'); ?></td>
                                        <td><?php echo htmlspecialchars($message['phone_number']); ?></td>
                                        <td>
                                            <span class="badge bg-info">
                                                <?php echo htmlspecialchars($message['message_type']); ?>
                                            </span>
                                        </td>
                                        <td><?php echo htmlspecialchars(mb_substr($message['content'], 0, 50)) . '...'; ?></td>
                                        <td>
                                            <?php if ($message['direction'] === 'incoming'): ?>
                                                <span class="badge bg-success">واردة</span>
                                            <?php else: ?>
                                                <span class="badge bg-primary">صادرة</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?php echo $message['status'] === 'read' ? 'success' : 'warning'; ?>">
                                                <?php echo $message['status']; ?>
                                            </span>
                                        </td>
                                        <td><?php echo date('Y-m-d H:i', strtotime($message['created_at'])); ?></td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="assets/js/dashboard.js"></script>
</body>
</html>
