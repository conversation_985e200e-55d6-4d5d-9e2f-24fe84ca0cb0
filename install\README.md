# دليل التثبيت التلقائي
# Automatic Installation Guide

## 🚀 طرق التثبيت المتاحة

يوفر نظام أتمتة WhatsApp Business ثلاث طرق للتثبيت:

### 1. التثبيت عبر الواجهة الرسومية (مُوصى به)
```bash
# انتقل إلى مجلد المشروع
cd /path/to/whatsapp-automation

# افتح المتصفح واذهب إلى:
http://yourdomain.com/install/
```

### 2. التثبيت من سطر الأوامر
```bash
# تشغيل معالج التثبيت التفاعلي
php install/cli_install.php
```

### 3. التثبيت السريع (Linux/macOS)
```bash
# جعل السكريبت قابل للتنفيذ
chmod +x install.sh

# تشغيل التثبيت السريع
./install.sh
```

## 📋 المتطلبات الأساسية

### متطلبات الخادم
- **PHP 8.0+** مع الإضافات التالية:
  - PDO MySQL
  - cURL
  - JSON
  - mbstring
  - OpenSSL
- **MySQL 8.0+** أو **MariaDB 10.4+**
- **Apache 2.4+** أو **Nginx 1.18+**
- **SSL Certificate** (مطلوب لـ WhatsApp Webhook)

### متطلبات WhatsApp Business
- حساب WhatsApp Business مُفعل
- Facebook Business Manager مع صلاحيات الإدارة
- WhatsApp Business API مُعتمد من Meta

## 🔧 التثبيت التفصيلي

### الخطوة 1: تحضير الخادم

#### على Ubuntu/Debian:
```bash
# تحديث النظام
sudo apt update && sudo apt upgrade -y

# تثبيت Apache و MySQL و PHP
sudo apt install apache2 mysql-server php8.1 php8.1-mysql php8.1-curl php8.1-json php8.1-mbstring php8.1-xml php8.1-zip -y

# تفعيل mod_rewrite
sudo a2enmod rewrite

# إعادة تشغيل Apache
sudo systemctl restart apache2
```

#### على CentOS/RHEL:
```bash
# تحديث النظام
sudo yum update -y

# تثبيت Apache و MySQL و PHP
sudo yum install httpd mariadb-server php php-mysql php-curl php-json php-mbstring php-xml -y

# تشغيل الخدمات
sudo systemctl start httpd mariadb
sudo systemctl enable httpd mariadb
```

### الخطوة 2: إعداد قاعدة البيانات

```bash
# تأمين MySQL
sudo mysql_secure_installation

# الدخول إلى MySQL
sudo mysql -u root -p

# إنشاء قاعدة البيانات والمستخدم
CREATE DATABASE whatsapp_automation CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'whatsapp_user'@'localhost' IDENTIFIED BY 'strong_password_here';
GRANT ALL PRIVILEGES ON whatsapp_automation.* TO 'whatsapp_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

### الخطوة 3: تحميل المشروع

```bash
# الانتقال إلى مجلد الويب
cd /var/www/html

# تحميل المشروع (استبدل بالرابط الصحيح)
git clone https://github.com/your-repo/whatsapp-automation.git

# أو رفع الملفات يدوياً
sudo mkdir whatsapp-automation
# ثم رفع الملفات
```

### الخطوة 4: تعيين الصلاحيات

```bash
# تغيير المالك
sudo chown -R www-data:www-data /var/www/html/whatsapp-automation/

# تعيين الصلاحيات
sudo chmod 755 -R /var/www/html/whatsapp-automation/
sudo chmod 777 -R /var/www/html/whatsapp-automation/logs/
sudo chmod 777 -R /var/www/html/whatsapp-automation/uploads/
sudo chmod 777 -R /var/www/html/whatsapp-automation/reports/
sudo chmod 777 -R /var/www/html/whatsapp-automation/backups/
```

### الخطوة 5: تشغيل التثبيت

اختر إحدى الطرق التالية:

#### أ) التثبيت عبر المتصفح:
1. اذهب إلى: `http://yourdomain.com/install/`
2. اتبع الخطوات في المعالج
3. أدخل بيانات قاعدة البيانات
4. أعد إعداد النظام
5. أنشئ حساب المدير

#### ب) التثبيت من سطر الأوامر:
```bash
cd /var/www/html/whatsapp-automation
php install/cli_install.php
```

#### ج) التثبيت السريع:
```bash
cd /var/www/html/whatsapp-automation
chmod +x install.sh
./install.sh
```

## ⚙️ إعداد خادم الويب

### Apache Virtual Host
```apache
<VirtualHost *:80>
    ServerName yourdomain.com
    DocumentRoot /var/www/html/whatsapp-automation
    
    <Directory /var/www/html/whatsapp-automation>
        AllowOverride All
        Require all granted
    </Directory>
    
    ErrorLog ${APACHE_LOG_DIR}/whatsapp_error.log
    CustomLog ${APACHE_LOG_DIR}/whatsapp_access.log combined
</VirtualHost>
```

### Nginx Configuration
```nginx
server {
    listen 80;
    server_name yourdomain.com;
    root /var/www/html/whatsapp-automation;
    index index.php index.html;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }

    location ~ /\.(ht|env) {
        deny all;
    }
}
```

## 🔒 إعداد SSL

### باستخدام Let's Encrypt:
```bash
# تثبيت Certbot
sudo apt install certbot python3-certbot-apache -y

# الحصول على شهادة SSL
sudo certbot --apache -d yourdomain.com

# تجديد تلقائي
sudo crontab -e
# إضافة السطر التالي:
0 12 * * * /usr/bin/certbot renew --quiet
```

## 📱 إعداد WhatsApp Business API

### 1. إنشاء تطبيق Facebook
1. اذهب إلى [Facebook for Developers](https://developers.facebook.com/)
2. أنشئ تطبيق جديد من نوع "Business"
3. أضف منتج "WhatsApp Business Platform"

### 2. الحصول على المفاتيح
- **Access Token**: من Facebook Developers Console
- **Phone Number ID**: معرف رقم الهاتف في WhatsApp Business
- **Webhook Verify Token**: رمز مخصص للتحقق

### 3. إعداد Webhook
- **URL**: `https://yourdomain.com/webhook.php`
- **Verify Token**: الرمز المُعرّف في الإعدادات
- **Fields**: `messages`, `message_deliveries`, `message_reads`

## 🕐 إعداد المهام المجدولة

```bash
# تحرير crontab
sudo crontab -e

# إضافة المهام التالية:
# معالجة الرسائل المجدولة كل 5 دقائق
*/5 * * * * /usr/bin/php /var/www/html/whatsapp-automation/cron/scheduler.php

# تنظيف البيانات يومياً في الساعة 2:00 صباحاً
0 2 * * * /usr/bin/php /var/www/html/whatsapp-automation/cron/cleanup.php

# نسخة احتياطية يومياً في الساعة 3:00 صباحاً
0 3 * * * /var/www/html/whatsapp-automation/scripts/backup.sh
```

## ✅ اختبار التثبيت

### 1. اختبار قاعدة البيانات
```bash
mysql -u whatsapp_user -p whatsapp_automation -e "SHOW TABLES;"
```

### 2. اختبار خادم الويب
```bash
curl -I http://yourdomain.com
```

### 3. اختبار PHP
```bash
php -v
php -m | grep -E "(pdo_mysql|curl|json|mbstring|openssl)"
```

### 4. تشغيل اختبارات النظام
```bash
cd /var/www/html/whatsapp-automation
php tests/test_system.php
```

## 🚨 استكشاف الأخطاء

### مشاكل شائعة وحلولها:

#### خطأ 500 - Internal Server Error
```bash
# تحقق من سجلات Apache
sudo tail -f /var/log/apache2/error.log

# تحقق من صلاحيات الملفات
sudo chown -R www-data:www-data /var/www/html/whatsapp-automation/
```

#### خطأ في قاعدة البيانات
```bash
# تحقق من حالة MySQL
sudo systemctl status mysql

# اختبار الاتصال
mysql -u whatsapp_user -p -e "SELECT 1;"
```

#### مشاكل الصلاحيات
```bash
# إعادة تعيين الصلاحيات
sudo chmod -R 755 /var/www/html/whatsapp-automation/
sudo chmod -R 777 /var/www/html/whatsapp-automation/logs/
sudo chmod -R 777 /var/www/html/whatsapp-automation/uploads/
```

## 📞 الحصول على المساعدة

### قنوات الدعم:
- **GitHub Issues**: [رابط المشروع]
- **البريد الإلكتروني**: <EMAIL>
- **التوثيق**: راجع ملف README.md الرئيسي

### قبل طلب المساعدة:
1. راجع هذا الدليل
2. تحقق من سجلات الأخطاء
3. جرب تشغيل اختبارات النظام
4. جهز معلومات البيئة (PHP version, OS, etc.)

---

**بعد إكمال التثبيت، احذف مجلد `install/` لأسباب أمنية!**
