<?php
/**
 * محرك الأتمتة المتقدم
 * Advanced Automation Engine
 */

class AutomationEngine {
    private $database;
    private $whatsappAPI;
    
    public function __construct($database, $whatsappAPI) {
        $this->database = $database;
        $this->whatsappAPI = $whatsappAPI;
    }
    
    /**
     * معالج الردود الذكية
     * Smart Reply Handler
     */
    public function processSmartReply($phoneNumber, $message, $context = []) {
        // تحليل المشاعر الأساسي
        $sentiment = $this->analyzeSentiment($message);
        
        // استخراج الكلمات المفتاحية
        $keywords = $this->extractKeywords($message);
        
        // تحديد نوع الاستفسار
        $queryType = $this->classifyQuery($message, $keywords);
        
        // البحث عن رد مناسب
        $response = $this->findSmartResponse($queryType, $keywords, $sentiment, $context);
        
        if ($response) {
            // تخصيص الرد
            $personalizedResponse = $this->personalizeResponse($response, $phoneNumber, $context);
            
            // إرسال الرد
            $this->whatsappAPI->sendTextMessage($phoneNumber, $personalizedResponse, true);
            
            // تسجيل الرد الذكي
            $this->logSmartReply($phoneNumber, $message, $personalizedResponse, $queryType, $sentiment);
            
            return true;
        }
        
        return false;
    }
    
    /**
     * تحليل المشاعر الأساسي
     * Basic Sentiment Analysis
     */
    private function analyzeSentiment($text) {
        $positiveWords = ['شكرا', 'ممتاز', 'رائع', 'جيد', 'أحب', 'سعيد', 'راضي', 'مبسوط'];
        $negativeWords = ['سيء', 'مشكلة', 'خطأ', 'غاضب', 'زعلان', 'مستاء', 'لا يعمل', 'معطل'];
        
        $text = mb_strtolower($text);
        $positiveCount = 0;
        $negativeCount = 0;
        
        foreach ($positiveWords as $word) {
            if (mb_strpos($text, $word) !== false) {
                $positiveCount++;
            }
        }
        
        foreach ($negativeWords as $word) {
            if (mb_strpos($text, $word) !== false) {
                $negativeCount++;
            }
        }
        
        if ($positiveCount > $negativeCount) {
            return 'positive';
        } elseif ($negativeCount > $positiveCount) {
            return 'negative';
        } else {
            return 'neutral';
        }
    }
    
    /**
     * استخراج الكلمات المفتاحية
     * Extract Keywords
     */
    private function extractKeywords($text) {
        // إزالة كلمات الوقف العربية
        $stopWords = ['في', 'من', 'إلى', 'على', 'عن', 'مع', 'هذا', 'هذه', 'التي', 'الذي', 'كان', 'كانت'];
        
        $words = preg_split('/\s+/', mb_strtolower($text));
        $keywords = [];
        
        foreach ($words as $word) {
            $word = trim($word, '.,!?؟،');
            if (mb_strlen($word) > 2 && !in_array($word, $stopWords)) {
                $keywords[] = $word;
            }
        }
        
        return array_unique($keywords);
    }
    
    /**
     * تصنيف نوع الاستفسار
     * Classify Query Type
     */
    private function classifyQuery($message, $keywords) {
        $queryPatterns = [
            'price' => ['سعر', 'كم', 'تكلفة', 'ثمن', 'مبلغ'],
            'support' => ['مشكلة', 'خطأ', 'لا يعمل', 'مساعدة', 'دعم'],
            'info' => ['معلومات', 'تفاصيل', 'شرح', 'كيف', 'ماذا', 'متى'],
            'order' => ['طلب', 'شراء', 'أريد', 'أحتاج'],
            'complaint' => ['شكوى', 'اعتراض', 'غير راضي', 'مشكلة'],
            'greeting' => ['مرحبا', 'السلام', 'أهلا', 'صباح', 'مساء']
        ];
        
        foreach ($queryPatterns as $type => $patterns) {
            foreach ($patterns as $pattern) {
                if (mb_strpos(mb_strtolower($message), $pattern) !== false) {
                    return $type;
                }
            }
        }
        
        return 'general';
    }
    
    /**
     * البحث عن رد ذكي
     * Find Smart Response
     */
    private function findSmartResponse($queryType, $keywords, $sentiment, $context) {
        // البحث في قاعدة البيانات عن ردود مناسبة
        $responses = $this->database->fetchAll("
            SELECT * FROM auto_replies 
            WHERE is_active = 1 
            AND JSON_CONTAINS(conditions, JSON_OBJECT('query_type', ?))
            ORDER BY priority DESC
        ", [$queryType]);
        
        if (empty($responses)) {
            // البحث عن ردود عامة
            $responses = $this->database->fetchAll("
                SELECT * FROM auto_replies 
                WHERE is_active = 1 
                AND JSON_CONTAINS(conditions, JSON_OBJECT('query_type', 'general'))
                ORDER BY priority DESC
            ");
        }
        
        foreach ($responses as $response) {
            $conditions = json_decode($response['conditions'], true);
            
            // التحقق من الشروط الإضافية
            if ($this->checkConditions($conditions, $sentiment, $context)) {
                return $response['response_text'];
            }
        }
        
        return null;
    }
    
    /**
     * التحقق من الشروط
     * Check Conditions
     */
    private function checkConditions($conditions, $sentiment, $context) {
        if (isset($conditions['sentiment']) && $conditions['sentiment'] !== $sentiment) {
            return false;
        }
        
        if (isset($conditions['business_hours']) && $conditions['business_hours'] && !$this->isBusinessHours()) {
            return false;
        }
        
        return true;
    }
    
    /**
     * تخصيص الرد
     * Personalize Response
     */
    private function personalizeResponse($response, $phoneNumber, $context) {
        // الحصول على معلومات جهة الاتصال
        $contact = $this->database->fetchOne(
            "SELECT * FROM contacts WHERE phone_number = ?",
            [$phoneNumber]
        );
        
        $name = $contact['name'] ?? 'عزيزي العميل';
        
        // استبدال المتغيرات
        $response = str_replace('{name}', $name, $response);
        $response = str_replace('{business_name}', getSetting('business_name', 'شركتنا'), $response);
        $response = str_replace('{time}', date('H:i'), $response);
        $response = str_replace('{date}', date('Y-m-d'), $response);
        
        return $response;
    }
    
    /**
     * جدولة الرسائل التلقائية
     * Schedule Automated Messages
     */
    public function scheduleFollowUpMessages($phoneNumber, $messageType = 'welcome') {
        $followUpTemplates = [
            'welcome' => [
                ['delay' => 60, 'message' => 'شكراً لتواصلك معنا! هل تحتاج إلى مساعدة إضافية؟'],
                ['delay' => 1440, 'message' => 'نتمنى أن تكون راضياً عن خدماتنا. لا تتردد في التواصل معنا.']
            ],
            'support' => [
                ['delay' => 30, 'message' => 'هل تم حل مشكلتك؟ نحن هنا لمساعدتك.'],
                ['delay' => 720, 'message' => 'نود التأكد من رضاك عن الحل المقدم. تقييمك مهم لنا.']
            ]
        ];
        
        if (!isset($followUpTemplates[$messageType])) {
            return false;
        }
        
        foreach ($followUpTemplates[$messageType] as $followUp) {
            $scheduledTime = date('Y-m-d H:i:s', time() + ($followUp['delay'] * 60));
            
            $this->database->insert('scheduled_messages', [
                'contact_id' => $this->getContactId($phoneNumber),
                'message_content' => $followUp['message'],
                'scheduled_time' => $scheduledTime,
                'status' => 'pending',
                'created_at' => date('Y-m-d H:i:s')
            ]);
        }
        
        return true;
    }
    
    /**
     * معالج الرسائل المجدولة
     * Process Scheduled Messages
     */
    public function processScheduledMessages() {
        $scheduledMessages = $this->database->fetchAll("
            SELECT sm.*, c.phone_number 
            FROM scheduled_messages sm
            JOIN contacts c ON sm.contact_id = c.id
            WHERE sm.status = 'pending' 
            AND sm.scheduled_time <= NOW()
            ORDER BY sm.scheduled_time ASC
            LIMIT 50
        ");
        
        foreach ($scheduledMessages as $message) {
            try {
                $messageId = $this->whatsappAPI->sendTextMessage(
                    $message['phone_number'],
                    $message['message_content'],
                    true
                );
                
                if ($messageId) {
                    $this->database->update(
                        'scheduled_messages',
                        [
                            'status' => 'sent',
                            'sent_at' => date('Y-m-d H:i:s')
                        ],
                        'id = ?',
                        [$message['id']]
                    );
                } else {
                    $this->database->update(
                        'scheduled_messages',
                        ['status' => 'failed'],
                        'id = ?',
                        [$message['id']]
                    );
                }
                
                // تأخير قصير لتجنب تجاوز حدود API
                sleep(1);
                
            } catch (Exception $e) {
                $this->database->update(
                    'scheduled_messages',
                    [
                        'status' => 'failed',
                        'error_message' => $e->getMessage()
                    ],
                    'id = ?',
                    [$message['id']]
                );
            }
        }
        
        return count($scheduledMessages);
    }
    
    /**
     * نظام تقييم العملاء
     * Customer Rating System
     */
    public function requestCustomerFeedback($phoneNumber, $serviceType = 'general') {
        $feedbackMessage = "نود معرفة رأيك في خدماتنا. يرجى تقييم تجربتك من 1 إلى 5:\n";
        $feedbackMessage .= "1 - غير راضي تماماً\n";
        $feedbackMessage .= "2 - غير راضي\n";
        $feedbackMessage .= "3 - مقبول\n";
        $feedbackMessage .= "4 - راضي\n";
        $feedbackMessage .= "5 - راضي جداً\n\n";
        $feedbackMessage .= "أرسل الرقم فقط للتقييم.";
        
        return $this->whatsappAPI->sendTextMessage($phoneNumber, $feedbackMessage, true);
    }
    
    /**
     * معالجة تقييم العميل
     * Process Customer Rating
     */
    public function processCustomerRating($phoneNumber, $rating) {
        if (!is_numeric($rating) || $rating < 1 || $rating > 5) {
            return false;
        }
        
        $contactId = $this->getContactId($phoneNumber);
        
        // حفظ التقييم
        $this->database->insert('customer_ratings', [
            'contact_id' => $contactId,
            'rating' => $rating,
            'created_at' => date('Y-m-d H:i:s')
        ]);
        
        // رد مناسب حسب التقييم
        if ($rating >= 4) {
            $response = "شكراً لك على التقييم الإيجابي! نسعد بخدمتك دائماً.";
        } elseif ($rating == 3) {
            $response = "شكراً لتقييمك. نعمل باستمرار على تحسين خدماتنا.";
        } else {
            $response = "نعتذر عن عدم رضاك. سيتواصل معك أحد ممثلي خدمة العملاء قريباً.";
            // إشعار فريق الدعم
            $this->notifySupport($phoneNumber, $rating);
        }
        
        return $this->whatsappAPI->sendTextMessage($phoneNumber, $response, true);
    }
    
    /**
     * الحصول على معرف جهة الاتصال
     * Get Contact ID
     */
    private function getContactId($phoneNumber) {
        $contact = $this->database->fetchOne(
            "SELECT id FROM contacts WHERE phone_number = ?",
            [$phoneNumber]
        );
        
        return $contact ? $contact['id'] : null;
    }
    
    /**
     * التحقق من ساعات العمل
     * Check Business Hours
     */
    private function isBusinessHours() {
        $startTime = getSetting('business_hours_start', '09:00');
        $endTime = getSetting('business_hours_end', '18:00');
        $currentTime = date('H:i');
        
        return ($currentTime >= $startTime && $currentTime <= $endTime);
    }
    
    /**
     * تسجيل الرد الذكي
     * Log Smart Reply
     */
    private function logSmartReply($phoneNumber, $originalMessage, $response, $queryType, $sentiment) {
        $this->database->insert('smart_replies_log', [
            'phone_number' => $phoneNumber,
            'original_message' => $originalMessage,
            'response' => $response,
            'query_type' => $queryType,
            'sentiment' => $sentiment,
            'created_at' => date('Y-m-d H:i:s')
        ]);
    }
    
    /**
     * إشعار فريق الدعم
     * Notify Support Team
     */
    private function notifySupport($phoneNumber, $rating) {
        // يمكن إضافة إشعارات عبر البريد الإلكتروني أو أنظمة أخرى
        $this->database->insert('support_notifications', [
            'phone_number' => $phoneNumber,
            'type' => 'low_rating',
            'data' => json_encode(['rating' => $rating]),
            'status' => 'pending',
            'created_at' => date('Y-m-d H:i:s')
        ]);
    }
}
?>
