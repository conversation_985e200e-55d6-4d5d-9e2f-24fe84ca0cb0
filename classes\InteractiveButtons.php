<?php
/**
 * فئة إدارة الأزرار التفاعلية
 * Interactive Buttons Manager Class
 */

class InteractiveButtons {
    private $database;
    private $whatsappAPI;
    
    public function __construct($database, $whatsappAPI) {
        $this->database = $database;
        $this->whatsappAPI = $whatsappAPI;
    }
    
    /**
     * إرسال قائمة الخدمات الرئيسية
     * Send main services menu
     */
    public function sendMainMenu($phoneNumber) {
        $buttons = [
            [
                'id' => 'services',
                'title' => '🛍️ خدماتنا'
            ],
            [
                'id' => 'support',
                'title' => '🆘 الدعم الفني'
            ],
            [
                'id' => 'contact',
                'title' => '📞 تواصل معنا'
            ]
        ];
        
        return $this->whatsappAPI->sendButtonMessage(
            $phoneNumber,
            'مرحباً بك! كيف يمكنني مساعدتك اليوم؟',
            $buttons,
            'القائمة الرئيسية',
            'اختر أحد الخيارات أدناه'
        );
    }
    
    /**
     * إرسال قائمة الخدمات
     * Send services menu
     */
    public function sendServicesMenu($phoneNumber) {
        $sections = [
            [
                'title' => 'الخدمات الأساسية',
                'rows' => [
                    [
                        'id' => 'service_1',
                        'title' => 'خدمة التسويق الرقمي',
                        'description' => 'حلول تسويقية متكاملة'
                    ],
                    [
                        'id' => 'service_2',
                        'title' => 'تطوير المواقع',
                        'description' => 'تصميم وتطوير مواقع احترافية'
                    ],
                    [
                        'id' => 'service_3',
                        'title' => 'إدارة وسائل التواصل',
                        'description' => 'إدارة حسابات التواصل الاجتماعي'
                    ]
                ]
            ],
            [
                'title' => 'الخدمات المتقدمة',
                'rows' => [
                    [
                        'id' => 'service_4',
                        'title' => 'الذكاء الاصطناعي',
                        'description' => 'حلول ذكية للأعمال'
                    ],
                    [
                        'id' => 'service_5',
                        'title' => 'التجارة الإلكترونية',
                        'description' => 'منصات بيع إلكترونية'
                    ]
                ]
            ]
        ];
        
        return $this->whatsappAPI->sendListMessage(
            $phoneNumber,
            'اختر الخدمة التي تهمك من القائمة أدناه:',
            'عرض الخدمات',
            $sections,
            'خدماتنا المتميزة',
            'للمزيد من المعلومات اختر خدمة'
        );
    }
    
    /**
     * إرسال خيارات الدعم الفني
     * Send support options
     */
    public function sendSupportMenu($phoneNumber) {
        $buttons = [
            [
                'id' => 'tech_support',
                'title' => '🔧 دعم تقني'
            ],
            [
                'id' => 'billing_support',
                'title' => '💰 دعم مالي'
            ],
            [
                'id' => 'general_inquiry',
                'title' => '❓ استفسار عام'
            ]
        ];
        
        return $this->whatsappAPI->sendButtonMessage(
            $phoneNumber,
            'ما نوع المساعدة التي تحتاجها؟',
            $buttons,
            'الدعم الفني',
            'فريقنا جاهز لمساعدتك'
        );
    }
    
    /**
     * إرسال خيارات التقييم
     * Send rating options
     */
    public function sendRatingButtons($phoneNumber) {
        $buttons = [
            [
                'id' => 'rating_excellent',
                'title' => '⭐⭐⭐⭐⭐ ممتاز'
            ],
            [
                'id' => 'rating_good',
                'title' => '⭐⭐⭐⭐ جيد'
            ],
            [
                'id' => 'rating_average',
                'title' => '⭐⭐⭐ متوسط'
            ]
        ];
        
        return $this->whatsappAPI->sendButtonMessage(
            $phoneNumber,
            'كيف تقيم تجربتك معنا؟',
            $buttons,
            'تقييم الخدمة',
            'رأيك يهمنا لتحسين خدماتنا'
        );
    }
    
    /**
     * إرسال خيارات الاتصال
     * Send contact options
     */
    public function sendContactOptions($phoneNumber) {
        $sections = [
            [
                'title' => 'طرق التواصل',
                'rows' => [
                    [
                        'id' => 'phone_call',
                        'title' => '📞 مكالمة هاتفية',
                        'description' => '+966-XX-XXX-XXXX'
                    ],
                    [
                        'id' => 'email_contact',
                        'title' => '📧 بريد إلكتروني',
                        'description' => '<EMAIL>'
                    ],
                    [
                        'id' => 'visit_office',
                        'title' => '🏢 زيارة المكتب',
                        'description' => 'الرياض، المملكة العربية السعودية'
                    ]
                ]
            ],
            [
                'title' => 'ساعات العمل',
                'rows' => [
                    [
                        'id' => 'working_hours',
                        'title' => '🕘 ساعات العمل',
                        'description' => 'السبت - الخميس: 9 ص - 6 م'
                    ]
                ]
            ]
        ];
        
        return $this->whatsappAPI->sendListMessage(
            $phoneNumber,
            'اختر طريقة التواصل المناسبة لك:',
            'طرق التواصل',
            $sections,
            'تواصل معنا',
            'نحن في خدمتك دائماً'
        );
    }
    
    /**
     * معالجة النقر على الأزرار
     * Handle button clicks
     */
    public function handleButtonClick($phoneNumber, $buttonId, $buttonTitle = '') {
        // تسجيل النقرة
        $this->logButtonClick($phoneNumber, $buttonId, $buttonTitle);
        
        switch ($buttonId) {
            case 'services':
                return $this->sendServicesMenu($phoneNumber);
                
            case 'support':
                return $this->sendSupportMenu($phoneNumber);
                
            case 'contact':
                return $this->sendContactOptions($phoneNumber);
                
            case 'tech_support':
                return $this->handleTechSupport($phoneNumber);
                
            case 'billing_support':
                return $this->handleBillingSupport($phoneNumber);
                
            case 'general_inquiry':
                return $this->handleGeneralInquiry($phoneNumber);
                
            case 'rating_excellent':
                return $this->handleRating($phoneNumber, 5);
                
            case 'rating_good':
                return $this->handleRating($phoneNumber, 4);
                
            case 'rating_average':
                return $this->handleRating($phoneNumber, 3);
                
            default:
                if (strpos($buttonId, 'service_') === 0) {
                    return $this->handleServiceInquiry($phoneNumber, $buttonId);
                }
                return $this->sendMainMenu($phoneNumber);
        }
    }
    
    /**
     * معالجة طلب الدعم التقني
     * Handle tech support request
     */
    private function handleTechSupport($phoneNumber) {
        $message = "تم تسجيل طلب الدعم التقني الخاص بك.\n\n";
        $message .= "سيتواصل معك أحد المختصين خلال 15 دقيقة.\n\n";
        $message .= "رقم التذكرة: #TECH" . time();
        
        // إشعار فريق الدعم
        $this->notifySupport($phoneNumber, 'tech_support');
        
        return $this->whatsappAPI->sendTextMessage($phoneNumber, $message, true);
    }
    
    /**
     * معالجة طلب الدعم المالي
     * Handle billing support request
     */
    private function handleBillingSupport($phoneNumber) {
        $message = "تم تسجيل استفسارك المالي.\n\n";
        $message .= "سيتواصل معك قسم المحاسبة خلال ساعة واحدة.\n\n";
        $message .= "رقم التذكرة: #BILL" . time();
        
        $this->notifySupport($phoneNumber, 'billing_support');
        
        return $this->whatsappAPI->sendTextMessage($phoneNumber, $message, true);
    }
    
    /**
     * معالجة الاستفسار العام
     * Handle general inquiry
     */
    private function handleGeneralInquiry($phoneNumber) {
        $message = "شكراً لاستفسارك!\n\n";
        $message .= "يمكنك كتابة استفسارك وسنرد عليك في أقرب وقت ممكن.\n\n";
        $message .= "أو يمكنك الاتصال بنا على: +966-XX-XXX-XXXX";
        
        return $this->whatsappAPI->sendTextMessage($phoneNumber, $message, true);
    }
    
    /**
     * معالجة التقييم
     * Handle rating
     */
    private function handleRating($phoneNumber, $rating) {
        // حفظ التقييم في قاعدة البيانات
        $contactId = $this->getContactId($phoneNumber);
        
        $this->database->insert('customer_ratings', [
            'contact_id' => $contactId,
            'rating' => $rating,
            'source' => 'interactive_button',
            'created_at' => date('Y-m-d H:i:s')
        ]);
        
        $message = "شكراً لك على التقييم! ";
        
        if ($rating >= 4) {
            $message .= "نسعد بأن خدماتنا نالت إعجابك. 😊";
        } else {
            $message .= "نعتذر إذا لم تكن الخدمة على مستوى توقعاتك. سنعمل على التحسين.";
            // إشعار الإدارة بالتقييم المنخفض
            $this->notifySupport($phoneNumber, 'low_rating', ['rating' => $rating]);
        }
        
        return $this->whatsappAPI->sendTextMessage($phoneNumber, $message, true);
    }
    
    /**
     * معالجة استفسار الخدمة
     * Handle service inquiry
     */
    private function handleServiceInquiry($phoneNumber, $serviceId) {
        $services = [
            'service_1' => 'التسويق الرقمي',
            'service_2' => 'تطوير المواقع',
            'service_3' => 'إدارة وسائل التواصل',
            'service_4' => 'الذكاء الاصطناعي',
            'service_5' => 'التجارة الإلكترونية'
        ];
        
        $serviceName = $services[$serviceId] ?? 'الخدمة المحددة';
        
        $message = "شكراً لاهتمامك بخدمة {$serviceName}!\n\n";
        $message .= "سيتواصل معك أحد مختصي المبيعات خلال 30 دقيقة لتقديم عرض مفصل.\n\n";
        $message .= "أو يمكنك زيارة موقعنا: www.company.com";
        
        // إشعار فريق المبيعات
        $this->notifySupport($phoneNumber, 'service_inquiry', ['service' => $serviceName]);
        
        return $this->whatsappAPI->sendTextMessage($phoneNumber, $message, true);
    }
    
    /**
     * تسجيل النقر على الزر
     * Log button click
     */
    private function logButtonClick($phoneNumber, $buttonId, $buttonTitle) {
        $contactId = $this->getContactId($phoneNumber);
        
        $this->database->insert('button_interactions', [
            'contact_id' => $contactId,
            'button_id' => $buttonId,
            'button_title' => $buttonTitle,
            'created_at' => date('Y-m-d H:i:s')
        ]);
    }
    
    /**
     * إشعار فريق الدعم
     * Notify support team
     */
    private function notifySupport($phoneNumber, $type, $data = []) {
        $this->database->insert('support_notifications', [
            'phone_number' => $phoneNumber,
            'type' => $type,
            'data' => json_encode($data),
            'status' => 'pending',
            'created_at' => date('Y-m-d H:i:s')
        ]);
    }
    
    /**
     * الحصول على معرف جهة الاتصال
     * Get contact ID
     */
    private function getContactId($phoneNumber) {
        $contact = $this->database->fetchOne(
            "SELECT id FROM contacts WHERE phone_number = ?",
            [$phoneNumber]
        );
        
        return $contact ? $contact['id'] : null;
    }
}
?>
