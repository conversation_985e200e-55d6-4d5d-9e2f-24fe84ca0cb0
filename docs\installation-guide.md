# دليل التثبيت المفصل
# Detailed Installation Guide

## 📋 قائمة المتطلبات

### متطلبات الخادم الأساسية
- **نظام التشغيل**: Linux (Ubuntu 20.04+ أو CentOS 8+)
- **ذاكرة الوصول العشوائي**: 2 جيجابايت كحد أدنى، 4 جيجابايت مُوصى به
- **مساحة التخزين**: 10 جيجابايت كحد أدنى
- **معالج**: 2 نواة كحد أدنى

### البرامج المطلوبة
```bash
# تحديث النظام
sudo apt update && sudo apt upgrade -y

# تثبيت Apache
sudo apt install apache2 -y

# تثبيت MySQL
sudo apt install mysql-server -y

# تثبيت PHP وإضافاته
sudo apt install php8.1 php8.1-mysql php8.1-curl php8.1-json php8.1-mbstring php8.1-xml php8.1-zip -y

# تثبيت Composer
curl -sS https://getcomposer.org/installer | php
sudo mv composer.phar /usr/local/bin/composer
```

## 🔧 إعداد قاعدة البيانات

### 1. تأمين MySQL
```bash
sudo mysql_secure_installation
```

### 2. إنشاء قاعدة البيانات والمستخدم
```sql
-- الدخول إلى MySQL
sudo mysql -u root -p

-- إنشاء قاعدة البيانات
CREATE DATABASE whatsapp_automation CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- إنشاء مستخدم مخصص
CREATE USER 'whatsapp_user'@'localhost' IDENTIFIED BY 'strong_password_here';

-- منح الصلاحيات
GRANT ALL PRIVILEGES ON whatsapp_automation.* TO 'whatsapp_user'@'localhost';

-- تطبيق التغييرات
FLUSH PRIVILEGES;

-- الخروج
EXIT;
```

### 3. استيراد هيكل قاعدة البيانات
```bash
mysql -u whatsapp_user -p whatsapp_automation < database/whatsapp_automation.sql
```

## 🌐 إعداد خادم الويب

### إعداد Apache Virtual Host
```bash
# إنشاء ملف التكوين
sudo nano /etc/apache2/sites-available/whatsapp-automation.conf
```

```apache
<VirtualHost *:80>
    ServerName yourdomain.com
    ServerAlias www.yourdomain.com
    DocumentRoot /var/www/whatsapp-automation
    
    <Directory /var/www/whatsapp-automation>
        AllowOverride All
        Require all granted
    </Directory>
    
    ErrorLog ${APACHE_LOG_DIR}/whatsapp-automation_error.log
    CustomLog ${APACHE_LOG_DIR}/whatsapp-automation_access.log combined
</VirtualHost>
```

```bash
# تفعيل الموقع
sudo a2ensite whatsapp-automation.conf

# تفعيل mod_rewrite
sudo a2enmod rewrite

# إعادة تشغيل Apache
sudo systemctl restart apache2
```

### إعداد SSL (Let's Encrypt)
```bash
# تثبيت Certbot
sudo apt install certbot python3-certbot-apache -y

# الحصول على شهادة SSL
sudo certbot --apache -d yourdomain.com -d www.yourdomain.com

# تجديد تلقائي
sudo crontab -e
# إضافة السطر التالي:
0 12 * * * /usr/bin/certbot renew --quiet
```

## 📁 تثبيت الملفات

### 1. تحميل المشروع
```bash
# الانتقال إلى مجلد الويب
cd /var/www/

# تحميل المشروع
git clone https://github.com/your-repo/whatsapp-automation.git

# أو رفع الملفات يدوياً
sudo mkdir whatsapp-automation
# ثم رفع الملفات
```

### 2. إعداد الصلاحيات
```bash
# تغيير المالك
sudo chown -R www-data:www-data /var/www/whatsapp-automation/

# إعداد صلاحيات المجلدات
sudo chmod 755 -R /var/www/whatsapp-automation/
sudo chmod 777 -R /var/www/whatsapp-automation/logs/
sudo chmod 777 -R /var/www/whatsapp-automation/uploads/
sudo chmod 777 -R /var/www/whatsapp-automation/reports/
sudo chmod 777 -R /var/www/whatsapp-automation/backups/

# حماية ملف الإعدادات
sudo chmod 600 /var/www/whatsapp-automation/.env
```

## ⚙️ تكوين النظام

### 1. إعداد ملف البيئة
```bash
cd /var/www/whatsapp-automation/
cp .env.example .env
nano .env
```

```env
# إعدادات التطبيق
APP_URL=https://yourdomain.com
APP_DEBUG=false

# إعدادات قاعدة البيانات
DB_HOST=localhost
DB_NAME=whatsapp_automation
DB_USERNAME=whatsapp_user
DB_PASSWORD=your_strong_password

# إعدادات WhatsApp Business API
WHATSAPP_ACCESS_TOKEN=your_permanent_access_token
WHATSAPP_PHONE_NUMBER_ID=your_phone_number_id
WHATSAPP_WEBHOOK_VERIFY_TOKEN=your_custom_verify_token

# إعدادات الأمان
JWT_SECRET=your-very-long-random-secret-key
ENCRYPTION_KEY=your-32-character-encryption-key

# إعدادات النظام
TIMEZONE=Asia/Riyadh
MAX_MESSAGES_PER_MINUTE=30
MAX_MESSAGES_PER_HOUR=100
```

### 2. إنشاء مفاتيح التشفير
```bash
# إنشاء مفتاح JWT عشوائي
openssl rand -base64 64

# إنشاء مفتاح التشفير
openssl rand -hex 32
```

## 🔗 إعداد WhatsApp Business API

### 1. إنشاء تطبيق Facebook
1. اذهب إلى [Facebook for Developers](https://developers.facebook.com/)
2. انقر "My Apps" ثم "Create App"
3. اختر "Business" كنوع التطبيق
4. أدخل اسم التطبيق ومعلومات الاتصال

### 2. إضافة WhatsApp Business Platform
1. في لوحة تحكم التطبيق، انقر "Add Product"
2. ابحث عن "WhatsApp Business Platform" وانقر "Set Up"
3. اتبع خطوات الإعداد

### 3. الحصول على Access Token
1. في قسم WhatsApp، اذهب إلى "Getting Started"
2. انسخ "Temporary Access Token"
3. لإنشاء Permanent Token:
   - اذهب إلى "System Users" في Business Manager
   - أنشئ مستخدم نظام جديد
   - اعطه صلاحيات WhatsApp Business Management
   - أنشئ Access Token دائم

### 4. إعداد Webhook
```bash
# URL الخاص بـ Webhook
https://yourdomain.com/webhook.php

# Verify Token
your_custom_verify_token_here

# Subscription Fields
messages
message_deliveries  
message_reads
message_echoes
```

### 5. اختبار الإعداد
```bash
# اختبار Webhook
curl -X GET "https://yourdomain.com/webhook.php?hub.mode=subscribe&hub.challenge=test&hub.verify_token=your_verify_token"

# يجب أن يعيد "test"
```

## 🕐 إعداد المهام المجدولة

### 1. إعداد Cron Jobs
```bash
# تحرير crontab
sudo crontab -e

# إضافة المهام التالية:
# معالجة الرسائل المجدولة كل 5 دقائق
*/5 * * * * /usr/bin/php /var/www/whatsapp-automation/cron/scheduler.php

# تنظيف البيانات يومياً في الساعة 2:00 صباحاً
0 2 * * * /usr/bin/php /var/www/whatsapp-automation/cron/cleanup.php

# نسخة احتياطية يومياً في الساعة 3:00 صباحاً
0 3 * * * /var/www/whatsapp-automation/scripts/backup.sh
```

### 2. إنشاء سكريبت النسخ الاحتياطي
```bash
nano /var/www/whatsapp-automation/scripts/backup.sh
```

```bash
#!/bin/bash
BACKUP_DIR="/var/www/whatsapp-automation/backups"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME="whatsapp_automation"
DB_USER="whatsapp_user"
DB_PASS="your_password"

# إنشاء نسخة احتياطية من قاعدة البيانات
mysqldump -u $DB_USER -p$DB_PASS $DB_NAME > $BACKUP_DIR/db_backup_$DATE.sql

# ضغط النسخة الاحتياطية
gzip $BACKUP_DIR/db_backup_$DATE.sql

# حذف النسخ الأقدم من 30 يوم
find $BACKUP_DIR -name "db_backup_*.sql.gz" -mtime +30 -delete

echo "Backup completed: db_backup_$DATE.sql.gz"
```

```bash
# جعل السكريبت قابل للتنفيذ
chmod +x /var/www/whatsapp-automation/scripts/backup.sh
```

## 🔒 تأمين النظام

### 1. إعداد Firewall
```bash
# تفعيل UFW
sudo ufw enable

# السماح بـ SSH
sudo ufw allow ssh

# السماح بـ HTTP و HTTPS
sudo ufw allow 80
sudo ufw allow 443

# حظر الوصول المباشر لملفات PHP الحساسة
sudo ufw deny from any to any port 80 proto tcp match comment "Block direct PHP access"
```

### 2. حماية ملفات النظام
```bash
# إنشاء ملف .htaccess لحماية المجلدات الحساسة
echo "Deny from all" | sudo tee /var/www/whatsapp-automation/config/.htaccess
echo "Deny from all" | sudo tee /var/www/whatsapp-automation/logs/.htaccess
echo "Deny from all" | sudo tee /var/www/whatsapp-automation/backups/.htaccess
```

### 3. إعداد مراقبة الأمان
```bash
# تثبيت fail2ban
sudo apt install fail2ban -y

# إنشاء تكوين مخصص
sudo nano /etc/fail2ban/jail.local
```

```ini
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 5

[apache-auth]
enabled = true

[apache-badbots]
enabled = true

[apache-noscript]
enabled = true

[apache-overflows]
enabled = true
```

## ✅ اختبار التثبيت

### 1. اختبار قاعدة البيانات
```bash
mysql -u whatsapp_user -p whatsapp_automation -e "SHOW TABLES;"
```

### 2. اختبار خادم الويب
```bash
curl -I https://yourdomain.com
```

### 3. اختبار WhatsApp API
```bash
# اختبار إرسال رسالة تجريبية
curl -X POST "https://graph.facebook.com/v18.0/YOUR_PHONE_NUMBER_ID/messages" \
-H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
-H "Content-Type: application/json" \
-d '{
  "messaging_product": "whatsapp",
  "to": "YOUR_TEST_NUMBER",
  "type": "text",
  "text": {
    "body": "اختبار النظام - تم التثبيت بنجاح!"
  }
}'
```

### 4. اختبار تسجيل الدخول
1. اذهب إلى `https://yourdomain.com/admin/`
2. استخدم:
   - **اسم المستخدم**: `admin`
   - **كلمة المرور**: `admin123`
3. غيّر كلمة المرور فوراً من الإعدادات

## 🚨 استكشاف مشاكل التثبيت

### مشكلة: لا يمكن الوصول للموقع
```bash
# تحقق من حالة Apache
sudo systemctl status apache2

# تحقق من السجلات
sudo tail -f /var/log/apache2/error.log
```

### مشكلة: خطأ في قاعدة البيانات
```bash
# تحقق من حالة MySQL
sudo systemctl status mysql

# اختبار الاتصال
mysql -u whatsapp_user -p -e "SELECT 1;"
```

### مشكلة: مشاكل الصلاحيات
```bash
# إعادة تعيين الصلاحيات
sudo chown -R www-data:www-data /var/www/whatsapp-automation/
sudo chmod -R 755 /var/www/whatsapp-automation/
sudo chmod -R 777 /var/www/whatsapp-automation/logs/
```

## 📞 الحصول على المساعدة

إذا واجهت أي مشاكل أثناء التثبيت:

1. **راجع السجلات**: تحقق من ملفات السجلات في `/var/log/`
2. **تحقق من المتطلبات**: تأكد من تثبيت جميع المتطلبات
3. **اتصل بالدعم**: راسلنا على <EMAIL>

---

**بعد إكمال هذه الخطوات، سيكون نظام أتمتة WhatsApp Business جاهزاً للاستخدام!**
