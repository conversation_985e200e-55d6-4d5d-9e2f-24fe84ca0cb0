require('dotenv').config();

const config = {
  // Environment
  NODE_ENV: process.env.NODE_ENV || 'development',
  PORT: parseInt(process.env.PORT) || 3000,

  // Business Settings
  business: {
    name: process.env.BUSINESS_NAME || 'Your Business',
    hoursStart: process.env.BUSINESS_HOURS_START || '09:00',
    hoursEnd: process.env.BUSINESS_HOURS_END || '18:00',
    timezone: process.env.TIMEZONE || 'UTC'
  },

  // Auto-Reply Settings
  autoReply: {
    enabled: process.env.AUTO_REPLY_ENABLED === 'true',
    welcomeMessage: process.env.WELCOME_MESSAGE || 'مرحباً! شكراً لتواصلك معنا. سنرد عليك في أقرب وقت ممكن.',
    outOfHoursMessage: process.env.OUT_OF_HOURS_MESSAGE || 'نشكرك لتواصلك معنا. ساعات العمل من 9 صباحاً إلى 6 مساءً. سنرد عليك خلال ساعات العمل.'
  },

  // Rate Limiting
  rateLimiting: {
    maxMessagesPerMinute: parseInt(process.env.MAX_MESSAGES_PER_MINUTE) || 30,
    maxMessagesPerHour: parseInt(process.env.MAX_MESSAGES_PER_HOUR) || 100
  },

  // Features
  features: {
    messageScheduling: process.env.ENABLE_MESSAGE_SCHEDULING === 'true',
    contactManagement: process.env.ENABLE_CONTACT_MANAGEMENT === 'true',
    groupManagement: process.env.ENABLE_GROUP_MANAGEMENT === 'true',
    broadcastLists: process.env.ENABLE_BROADCAST_LISTS === 'true'
  },

  // Logging
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    file: process.env.LOG_FILE || 'logs/whatsapp-automation.log'
  },

  // Paths
  paths: {
    data: './data',
    logs: './logs',
    session: './data/session',
    contacts: './data/contacts.json',
    messages: './data/messages.json',
    autoReplies: './data/auto-replies.json'
  },

  // WhatsApp Client Settings
  whatsapp: {
    sessionPath: './data/session',
    puppeteerOptions: {
      headless: true,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--single-process',
        '--disable-gpu'
      ]
    }
  }
};

module.exports = config;
