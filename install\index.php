<?php
/**
 * معالج التثبيت التلقائي
 * Automatic Installation Handler
 */

session_start();

// التحقق من وجود ملف التكوين (إذا كان موجوداً، فالنظام مثبت بالفعل)
if (file_exists('../config/config.php') && !isset($_GET['force'])) {
    header('Location: ../admin/');
    exit;
}

$step = $_GET['step'] ?? 1;
$errors = [];
$success = [];

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تثبيت نظام أتمتة WhatsApp Business</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #25D366 0%, #128C7E 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .install-container {
            max-width: 800px;
            margin: 50px auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .install-header {
            background: #25D366;
            color: white;
            padding: 30px;
            text-align: center;
        }
        .install-body {
            padding: 40px;
        }
        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
        }
        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 10px;
            font-weight: bold;
            position: relative;
        }
        .step.active {
            background: #25D366;
            color: white;
        }
        .step.completed {
            background: #28a745;
            color: white;
        }
        .step.pending {
            background: #e9ecef;
            color: #6c757d;
        }
        .step::after {
            content: '';
            position: absolute;
            top: 50%;
            right: -25px;
            width: 20px;
            height: 2px;
            background: #e9ecef;
            transform: translateY(-50%);
        }
        .step:last-child::after {
            display: none;
        }
        .requirement {
            display: flex;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        .requirement:last-child {
            border-bottom: none;
        }
        .requirement-icon {
            width: 30px;
            text-align: center;
            margin-left: 15px;
        }
        .btn-install {
            background: #25D366;
            border: none;
            padding: 12px 30px;
            border-radius: 8px;
            font-weight: bold;
        }
        .btn-install:hover {
            background: #128C7E;
        }
        .progress-bar {
            background: #25D366;
        }
    </style>
</head>
<body>
    <div class="install-container">
        <div class="install-header">
            <i class="fab fa-whatsapp fa-3x mb-3"></i>
            <h2>نظام أتمتة WhatsApp Business</h2>
            <p class="mb-0">معالج التثبيت التلقائي</p>
        </div>
        
        <div class="install-body">
            <!-- مؤشر الخطوات -->
            <div class="step-indicator">
                <div class="step <?php echo $step >= 1 ? ($step == 1 ? 'active' : 'completed') : 'pending'; ?>">1</div>
                <div class="step <?php echo $step >= 2 ? ($step == 2 ? 'active' : 'completed') : 'pending'; ?>">2</div>
                <div class="step <?php echo $step >= 3 ? ($step == 3 ? 'active' : 'completed') : 'pending'; ?>">3</div>
                <div class="step <?php echo $step >= 4 ? ($step == 4 ? 'active' : 'completed') : 'pending'; ?>">4</div>
                <div class="step <?php echo $step >= 5 ? ($step == 5 ? 'active' : 'completed') : 'pending'; ?>">5</div>
            </div>

            <?php
            switch ($step) {
                case 1:
                    include 'steps/step1_requirements.php';
                    break;
                case 2:
                    include 'steps/step2_database.php';
                    break;
                case 3:
                    include 'steps/step3_config.php';
                    break;
                case 4:
                    include 'steps/step4_admin.php';
                    break;
                case 5:
                    include 'steps/step5_complete.php';
                    break;
                default:
                    include 'steps/step1_requirements.php';
            }
            ?>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تحديث شريط التقدم
        function updateProgress(percent) {
            const progressBar = document.querySelector('.progress-bar');
            if (progressBar) {
                progressBar.style.width = percent + '%';
                progressBar.setAttribute('aria-valuenow', percent);
            }
        }

        // التحقق من المتطلبات تلقائياً
        function checkRequirements() {
            fetch('ajax/check_requirements.php')
                .then(response => response.json())
                .then(data => {
                    Object.keys(data).forEach(requirement => {
                        const element = document.getElementById(requirement);
                        if (element) {
                            const icon = element.querySelector('.requirement-icon i');
                            if (data[requirement]) {
                                icon.className = 'fas fa-check-circle text-success';
                            } else {
                                icon.className = 'fas fa-times-circle text-danger';
                            }
                        }
                    });
                })
                .catch(error => console.error('Error:', error));
        }

        // تشغيل فحص المتطلبات عند تحميل الصفحة
        if (<?php echo $step; ?> === 1) {
            checkRequirements();
        }
    </script>
</body>
</html>
