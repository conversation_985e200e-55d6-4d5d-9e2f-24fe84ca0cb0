<?php
/**
 * اختبار الاتصال بقاعدة البيانات عبر AJAX
 * AJAX Database Connection Test
 */

header('Content-Type: application/json');

if ($_POST && $_POST['action'] === 'test_connection') {
    $db_host = trim($_POST['db_host'] ?? '');
    $db_name = trim($_POST['db_name'] ?? '');
    $db_username = trim($_POST['db_username'] ?? '');
    $db_password = $_POST['db_password'] ?? '';
    
    try {
        // محاولة الاتصال بقاعدة البيانات
        $pdo = new PDO(
            "mysql:host={$db_host};dbname={$db_name};charset=utf8mb4",
            $db_username,
            $db_password,
            [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_TIMEOUT => 5
            ]
        );
        
        // اختبار بسيط
        $pdo->query("SELECT 1");
        
        echo json_encode([
            'success' => true,
            'message' => 'تم الاتصال بقاعدة البيانات بنجاح'
        ]);
        
    } catch (PDOException $e) {
        echo json_encode([
            'success' => false,
            'error' => $e->getMessage()
        ]);
    }
} else {
    echo json_encode([
        'success' => false,
        'error' => 'طلب غير صحيح'
    ]);
}
?>
