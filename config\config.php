<?php
/**
 * إعدادات النظام الرئيسية
 * Main System Configuration
 */

// تحميل متغيرات البيئة
if (file_exists(__DIR__ . '/../.env')) {
    $lines = file(__DIR__ . '/../.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) continue;
        if (strpos($line, '=') === false) continue;
        list($name, $value) = explode('=', $line, 2);
        $_ENV[trim($name)] = trim($value);
    }
}

// إعدادات التطبيق
define('APP_NAME', 'نظام أتمتة WhatsApp Business');
define('APP_VERSION', '1.0.0');
define('APP_URL', $_ENV['APP_URL'] ?? 'http://localhost');
define('APP_DEBUG', $_ENV['APP_DEBUG'] ?? false);

// إعدادات قاعدة البيانات
define('DB_HOST', $_ENV['DB_HOST'] ?? 'localhost');
define('DB_NAME', $_ENV['DB_NAME'] ?? 'whatsapp_automation');
define('DB_USERNAME', $_ENV['DB_USERNAME'] ?? 'root');
define('DB_PASSWORD', $_ENV['DB_PASSWORD'] ?? 'root');

// إعدادات WhatsApp Business API
define('WHATSAPP_API_URL', 'https://graph.facebook.com/v18.0/');
define('WHATSAPP_ACCESS_TOKEN', $_ENV['WHATSAPP_ACCESS_TOKEN'] ?? '');
define('WHATSAPP_PHONE_NUMBER_ID', $_ENV['WHATSAPP_PHONE_NUMBER_ID'] ?? '');
define('WHATSAPP_WEBHOOK_VERIFY_TOKEN', $_ENV['WHATSAPP_WEBHOOK_VERIFY_TOKEN'] ?? '');

// إعدادات الأمان
define('JWT_SECRET', $_ENV['JWT_SECRET'] ?? 'your-secret-key-here');
define('SESSION_LIFETIME', 3600 * 24); // 24 ساعة
define('PASSWORD_MIN_LENGTH', 8);

// إعدادات الملفات
define('UPLOAD_PATH', __DIR__ . '/../uploads/');
define('MAX_FILE_SIZE', 10 * 1024 * 1024); // 10 MB
define('ALLOWED_FILE_TYPES', ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx', 'mp3', 'mp4']);

// إعدادات الرسائل
define('MAX_MESSAGES_PER_MINUTE', $_ENV['MAX_MESSAGES_PER_MINUTE'] ?? 30);
define('MAX_MESSAGES_PER_HOUR', $_ENV['MAX_MESSAGES_PER_HOUR'] ?? 100);
define('MESSAGE_RETRY_ATTEMPTS', 3);

// إعدادات التوقيت
define('DEFAULT_TIMEZONE', $_ENV['TIMEZONE'] ?? 'Asia/Riyadh');
date_default_timezone_set(DEFAULT_TIMEZONE);

// إعدادات السجلات
define('LOG_PATH', __DIR__ . '/../logs/');
define('LOG_LEVEL', $_ENV['LOG_LEVEL'] ?? 'info');
define('LOG_MAX_FILES', 30);

// إعدادات التقارير
define('REPORTS_PATH', __DIR__ . '/../reports/');
define('REPORTS_CACHE_TIME', 300); // 5 دقائق

// إعدادات النسخ الاحتياطي
define('BACKUP_PATH', __DIR__ . '/../backups/');
define('BACKUP_RETENTION_DAYS', 30);

// رسائل النظام الافتراضية
define('DEFAULT_WELCOME_MESSAGE', 'مرحباً بك! شكراً لتواصلك معنا. سنرد عليك في أقرب وقت ممكن.');
define('DEFAULT_OUT_OF_HOURS_MESSAGE', 'شكراً لتواصلك معنا. ساعات العمل من 9 صباحاً إلى 6 مساءً. سنرد عليك خلال ساعات العمل.');
define('DEFAULT_ERROR_MESSAGE', 'عذراً، حدث خطأ. يرجى المحاولة مرة أخرى أو التواصل مع الدعم الفني.');

// إعدادات ساعات العمل الافتراضية
define('DEFAULT_BUSINESS_HOURS', [
    'start' => '09:00',
    'end' => '18:00',
    'days' => ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday']
]);

// إعدادات التخزين المؤقت
define('CACHE_ENABLED', $_ENV['CACHE_ENABLED'] ?? true);
define('CACHE_LIFETIME', 3600); // ساعة واحدة

// إعدادات الإشعارات
define('NOTIFICATIONS_ENABLED', $_ENV['NOTIFICATIONS_ENABLED'] ?? true);
define('EMAIL_NOTIFICATIONS', $_ENV['EMAIL_NOTIFICATIONS'] ?? false);

// إعدادات API
define('API_RATE_LIMIT', 100); // طلب في الدقيقة
define('API_VERSION', 'v1');

// إعدادات الصفحات
define('ITEMS_PER_PAGE', 20);
define('MAX_ITEMS_PER_PAGE', 100);

// إعدادات الأمان الإضافية
define('ENABLE_CSRF_PROTECTION', true);
define('ENABLE_XSS_PROTECTION', true);
define('ENABLE_SQL_INJECTION_PROTECTION', true);

// إعدادات التشفير
define('ENCRYPTION_METHOD', 'AES-256-CBC');
define('ENCRYPTION_KEY', $_ENV['ENCRYPTION_KEY'] ?? 'default-encryption-key');

// إنشاء المجلدات المطلوبة
$required_dirs = [
    UPLOAD_PATH,
    LOG_PATH,
    REPORTS_PATH,
    BACKUP_PATH
];

foreach ($required_dirs as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
}

// دالة للحصول على إعداد من قاعدة البيانات
function getSetting($key, $default = null) {
    global $database;
    try {
        $result = $database->fetchOne(
            "SELECT setting_value FROM system_settings WHERE setting_key = ?",
            [$key]
        );
        return $result ? $result['setting_value'] : $default;
    } catch (Exception $e) {
        return $default;
    }
}

// دالة لحفظ إعداد في قاعدة البيانات
function setSetting($key, $value, $description = '') {
    global $database;
    try {
        $existing = $database->fetchOne(
            "SELECT id FROM system_settings WHERE setting_key = ?",
            [$key]
        );
        
        if ($existing) {
            return $database->update(
                'system_settings',
                ['setting_value' => $value, 'updated_at' => date('Y-m-d H:i:s')],
                'setting_key = ?',
                [$key]
            );
        } else {
            return $database->insert('system_settings', [
                'setting_key' => $key,
                'setting_value' => $value,
                'description' => $description
            ]);
        }
    } catch (Exception $e) {
        return false;
    }
}

// تحميل إعدادات قاعدة البيانات
require_once __DIR__ . '/database.php';
?>
