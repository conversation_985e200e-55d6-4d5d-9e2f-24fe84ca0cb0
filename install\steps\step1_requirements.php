<?php
/**
 * الخطوة الأولى: فحص المتطلبات
 * Step 1: Requirements Check
 */

// فحص المتطلبات
$requirements = [
    'php_version' => version_compare(PHP_VERSION, '8.0.0', '>='),
    'pdo_mysql' => extension_loaded('pdo_mysql'),
    'curl' => extension_loaded('curl'),
    'json' => extension_loaded('json'),
    'mbstring' => extension_loaded('mbstring'),
    'openssl' => extension_loaded('openssl'),
    'write_config' => is_writable('../config/') || is_writable('../'),
    'write_logs' => is_writable('../logs/') || !file_exists('../logs/'),
    'write_uploads' => is_writable('../uploads/') || !file_exists('../uploads/'),
    'write_reports' => is_writable('../reports/') || !file_exists('../reports/'),
    'write_backups' => is_writable('../backups/') || !file_exists('../backups/')
];

$allRequirementsMet = !in_array(false, $requirements);
?>

<h3 class="mb-4">
    <i class="fas fa-clipboard-check text-primary"></i>
    فحص متطلبات النظام
</h3>

<p class="text-muted mb-4">
    يتم الآن فحص متطلبات النظام للتأكد من إمكانية تثبيت نظام أتمتة WhatsApp Business بنجاح.
</p>

<div class="requirements-list">
    <div class="requirement" id="php_version">
        <div class="requirement-icon">
            <i class="fas fa-<?php echo $requirements['php_version'] ? 'check-circle text-success' : 'times-circle text-danger'; ?>"></i>
        </div>
        <div class="requirement-details">
            <strong>PHP 8.0 أو أحدث</strong>
            <small class="d-block text-muted">الإصدار الحالي: <?php echo PHP_VERSION; ?></small>
        </div>
    </div>

    <div class="requirement" id="pdo_mysql">
        <div class="requirement-icon">
            <i class="fas fa-<?php echo $requirements['pdo_mysql'] ? 'check-circle text-success' : 'times-circle text-danger'; ?>"></i>
        </div>
        <div class="requirement-details">
            <strong>PDO MySQL Extension</strong>
            <small class="d-block text-muted">مطلوب للاتصال بقاعدة البيانات</small>
        </div>
    </div>

    <div class="requirement" id="curl">
        <div class="requirement-icon">
            <i class="fas fa-<?php echo $requirements['curl'] ? 'check-circle text-success' : 'times-circle text-danger'; ?>"></i>
        </div>
        <div class="requirement-details">
            <strong>cURL Extension</strong>
            <small class="d-block text-muted">مطلوب للاتصال بـ WhatsApp API</small>
        </div>
    </div>

    <div class="requirement" id="json">
        <div class="requirement-icon">
            <i class="fas fa-<?php echo $requirements['json'] ? 'check-circle text-success' : 'times-circle text-danger'; ?>"></i>
        </div>
        <div class="requirement-details">
            <strong>JSON Extension</strong>
            <small class="d-block text-muted">مطلوب لمعالجة البيانات</small>
        </div>
    </div>

    <div class="requirement" id="mbstring">
        <div class="requirement-icon">
            <i class="fas fa-<?php echo $requirements['mbstring'] ? 'check-circle text-success' : 'times-circle text-danger'; ?>"></i>
        </div>
        <div class="requirement-details">
            <strong>Multibyte String Extension</strong>
            <small class="d-block text-muted">مطلوب لدعم النصوص العربية</small>
        </div>
    </div>

    <div class="requirement" id="openssl">
        <div class="requirement-icon">
            <i class="fas fa-<?php echo $requirements['openssl'] ? 'check-circle text-success' : 'times-circle text-danger'; ?>"></i>
        </div>
        <div class="requirement-details">
            <strong>OpenSSL Extension</strong>
            <small class="d-block text-muted">مطلوب للاتصالات الآمنة</small>
        </div>
    </div>

    <div class="requirement" id="write_config">
        <div class="requirement-icon">
            <i class="fas fa-<?php echo $requirements['write_config'] ? 'check-circle text-success' : 'times-circle text-danger'; ?>"></i>
        </div>
        <div class="requirement-details">
            <strong>صلاحيات الكتابة - مجلد config</strong>
            <small class="d-block text-muted">مطلوب لحفظ إعدادات النظام</small>
        </div>
    </div>

    <div class="requirement" id="write_logs">
        <div class="requirement-icon">
            <i class="fas fa-<?php echo $requirements['write_logs'] ? 'check-circle text-success' : 'times-circle text-danger'; ?>"></i>
        </div>
        <div class="requirement-details">
            <strong>صلاحيات الكتابة - مجلد logs</strong>
            <small class="d-block text-muted">مطلوب لحفظ سجلات النظام</small>
        </div>
    </div>

    <div class="requirement" id="write_uploads">
        <div class="requirement-icon">
            <i class="fas fa-<?php echo $requirements['write_uploads'] ? 'check-circle text-success' : 'times-circle text-danger'; ?>"></i>
        </div>
        <div class="requirement-details">
            <strong>صلاحيات الكتابة - مجلد uploads</strong>
            <small class="d-block text-muted">مطلوب لرفع الملفات</small>
        </div>
    </div>

    <div class="requirement" id="write_reports">
        <div class="requirement-icon">
            <i class="fas fa-<?php echo $requirements['write_reports'] ? 'check-circle text-success' : 'times-circle text-danger'; ?>"></i>
        </div>
        <div class="requirement-details">
            <strong>صلاحيات الكتابة - مجلد reports</strong>
            <small class="d-block text-muted">مطلوب لحفظ التقارير</small>
        </div>
    </div>

    <div class="requirement" id="write_backups">
        <div class="requirement-icon">
            <i class="fas fa-<?php echo $requirements['write_backups'] ? 'check-circle text-success' : 'times-circle text-danger'; ?>"></i>
        </div>
        <div class="requirement-details">
            <strong>صلاحيات الكتابة - مجلد backups</strong>
            <small class="d-block text-muted">مطلوب للنسخ الاحتياطية</small>
        </div>
    </div>
</div>

<?php if (!$allRequirementsMet): ?>
    <div class="alert alert-danger mt-4">
        <i class="fas fa-exclamation-triangle"></i>
        <strong>تحذير!</strong> بعض المتطلبات غير متوفرة. يرجى إصلاح المشاكل المذكورة أعلاه قبل المتابعة.
        
        <div class="mt-3">
            <h6>إرشادات الإصلاح:</h6>
            <ul class="mb-0">
                <?php if (!$requirements['php_version']): ?>
                    <li>قم بترقية PHP إلى الإصدار 8.0 أو أحدث</li>
                <?php endif; ?>
                
                <?php if (!$requirements['pdo_mysql']): ?>
                    <li>قم بتفعيل إضافة PDO MySQL في PHP</li>
                <?php endif; ?>
                
                <?php if (!$requirements['curl']): ?>
                    <li>قم بتفعيل إضافة cURL في PHP</li>
                <?php endif; ?>
                
                <?php if (!$requirements['json']): ?>
                    <li>قم بتفعيل إضافة JSON في PHP</li>
                <?php endif; ?>
                
                <?php if (!$requirements['mbstring']): ?>
                    <li>قم بتفعيل إضافة mbstring في PHP</li>
                <?php endif; ?>
                
                <?php if (!$requirements['openssl']): ?>
                    <li>قم بتفعيل إضافة OpenSSL في PHP</li>
                <?php endif; ?>
                
                <?php if (!$requirements['write_config']): ?>
                    <li>قم بإعطاء صلاحيات الكتابة لمجلد config: <code>chmod 755 config/</code></li>
                <?php endif; ?>
                
                <?php if (!$requirements['write_logs']): ?>
                    <li>قم بإنشاء مجلد logs وإعطاؤه صلاحيات الكتابة: <code>mkdir logs && chmod 777 logs/</code></li>
                <?php endif; ?>
                
                <?php if (!$requirements['write_uploads']): ?>
                    <li>قم بإنشاء مجلد uploads وإعطاؤه صلاحيات الكتابة: <code>mkdir uploads && chmod 777 uploads/</code></li>
                <?php endif; ?>
                
                <?php if (!$requirements['write_reports']): ?>
                    <li>قم بإنشاء مجلد reports وإعطاؤه صلاحيات الكتابة: <code>mkdir reports && chmod 777 reports/</code></li>
                <?php endif; ?>
                
                <?php if (!$requirements['write_backups']): ?>
                    <li>قم بإنشاء مجلد backups وإعطاؤه صلاحيات الكتابة: <code>mkdir backups && chmod 777 backups/</code></li>
                <?php endif; ?>
            </ul>
        </div>
    </div>
    
    <div class="text-center mt-4">
        <button type="button" class="btn btn-secondary" onclick="location.reload()">
            <i class="fas fa-sync-alt"></i> إعادة فحص المتطلبات
        </button>
    </div>
<?php else: ?>
    <div class="alert alert-success mt-4">
        <i class="fas fa-check-circle"></i>
        <strong>ممتاز!</strong> جميع المتطلبات متوفرة. يمكنك المتابعة إلى الخطوة التالية.
    </div>
    
    <div class="text-center mt-4">
        <a href="?step=2" class="btn btn-success btn-install">
            <i class="fas fa-arrow-left"></i> المتابعة إلى إعداد قاعدة البيانات
        </a>
    </div>
<?php endif; ?>

<div class="progress mt-4">
    <div class="progress-bar" role="progressbar" style="width: 20%" aria-valuenow="20" aria-valuemin="0" aria-valuemax="100"></div>
</div>
