<?php
/**
 * الخطوة الثالثة: إعداد التكوين
 * Step 3: Configuration Setup
 */

$errors = [];
$success = [];

if ($_POST) {
    $app_url = trim($_POST['app_url'] ?? '');
    $business_name = trim($_POST['business_name'] ?? '');
    $whatsapp_token = trim($_POST['whatsapp_token'] ?? '');
    $whatsapp_phone_id = trim($_POST['whatsapp_phone_id'] ?? '');
    $webhook_token = trim($_POST['webhook_token'] ?? '');
    $timezone = $_POST['timezone'] ?? 'Asia/Riyadh';
    $business_hours_start = $_POST['business_hours_start'] ?? '09:00';
    $business_hours_end = $_POST['business_hours_end'] ?? '18:00';
    
    // التحقق من البيانات المطلوبة
    if (empty($app_url)) {
        $errors[] = 'رابط الموقع مطلوب';
    }
    
    if (empty($business_name)) {
        $errors[] = 'اسم الشركة مطلوب';
    }
    
    if (empty($webhook_token)) {
        $webhook_token = bin2hex(random_bytes(16));
        $success[] = 'تم إنشاء رمز Webhook تلقائياً';
    }
    
    if (empty($errors)) {
        // إنشاء ملف .env
        $envContent = "# إعدادات التطبيق\n";
        $envContent .= "APP_URL={$app_url}\n";
        $envContent .= "APP_DEBUG=false\n\n";
        
        $envContent .= "# إعدادات قاعدة البيانات\n";
        $envContent .= "DB_HOST={$_SESSION['db_config']['host']}\n";
        $envContent .= "DB_NAME={$_SESSION['db_config']['name']}\n";
        $envContent .= "DB_USERNAME={$_SESSION['db_config']['username']}\n";
        $envContent .= "DB_PASSWORD={$_SESSION['db_config']['password']}\n\n";
        
        $envContent .= "# إعدادات WhatsApp Business API\n";
        $envContent .= "WHATSAPP_ACCESS_TOKEN={$whatsapp_token}\n";
        $envContent .= "WHATSAPP_PHONE_NUMBER_ID={$whatsapp_phone_id}\n";
        $envContent .= "WHATSAPP_WEBHOOK_VERIFY_TOKEN={$webhook_token}\n\n";
        
        $envContent .= "# إعدادات الأمان\n";
        $envContent .= "JWT_SECRET=" . bin2hex(random_bytes(32)) . "\n";
        $envContent .= "ENCRYPTION_KEY=" . bin2hex(random_bytes(16)) . "\n\n";
        
        $envContent .= "# إعدادات النظام\n";
        $envContent .= "TIMEZONE={$timezone}\n";
        $envContent .= "MAX_MESSAGES_PER_MINUTE=30\n";
        $envContent .= "MAX_MESSAGES_PER_HOUR=100\n\n";
        
        $envContent .= "# إعدادات السجلات\n";
        $envContent .= "LOG_LEVEL=info\n\n";
        
        $envContent .= "# إعدادات التخزين المؤقت\n";
        $envContent .= "CACHE_ENABLED=true\n\n";
        
        $envContent .= "# إعدادات الإشعارات\n";
        $envContent .= "NOTIFICATIONS_ENABLED=true\n";
        $envContent .= "EMAIL_NOTIFICATIONS=false\n";
        
        // حفظ ملف .env
        if (file_put_contents('../.env', $envContent)) {
            $success[] = 'تم إنشاء ملف الإعدادات بنجاح';
            
            // حفظ الإعدادات في قاعدة البيانات
            try {
                $pdo = new PDO(
                    "mysql:host={$_SESSION['db_config']['host']};dbname={$_SESSION['db_config']['name']};charset=utf8mb4",
                    $_SESSION['db_config']['username'],
                    $_SESSION['db_config']['password'],
                    [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
                );
                
                // تحديث الإعدادات في قاعدة البيانات
                $settings = [
                    'business_name' => $business_name,
                    'business_hours_start' => $business_hours_start,
                    'business_hours_end' => $business_hours_end,
                    'timezone' => $timezone,
                    'whatsapp_api_token' => $whatsapp_token,
                    'whatsapp_phone_number_id' => $whatsapp_phone_id,
                    'webhook_verify_token' => $webhook_token
                ];
                
                foreach ($settings as $key => $value) {
                    $stmt = $pdo->prepare("UPDATE system_settings SET setting_value = ? WHERE setting_key = ?");
                    $stmt->execute([$value, $key]);
                }
                
                $success[] = 'تم حفظ الإعدادات في قاعدة البيانات';
                
                // حفظ بيانات التكوين في الجلسة
                $_SESSION['config'] = [
                    'business_name' => $business_name,
                    'webhook_token' => $webhook_token,
                    'app_url' => $app_url
                ];
                
                // إعادة توجيه للخطوة التالية
                header('Location: ?step=4');
                exit;
                
            } catch (PDOException $e) {
                $errors[] = 'خطأ في حفظ الإعدادات: ' . $e->getMessage();
            }
        } else {
            $errors[] = 'فشل في إنشاء ملف الإعدادات. تحقق من صلاحيات الكتابة';
        }
    }
}
?>

<h3 class="mb-4">
    <i class="fas fa-cog text-primary"></i>
    إعداد التكوين
</h3>

<p class="text-muted mb-4">
    أدخل إعدادات النظام الأساسية وبيانات WhatsApp Business API.
</p>

<?php if (!empty($errors)): ?>
    <div class="alert alert-danger">
        <i class="fas fa-exclamation-triangle"></i>
        <strong>خطأ!</strong>
        <ul class="mb-0 mt-2">
            <?php foreach ($errors as $error): ?>
                <li><?php echo htmlspecialchars($error); ?></li>
            <?php endforeach; ?>
        </ul>
    </div>
<?php endif; ?>

<?php if (!empty($success)): ?>
    <div class="alert alert-success">
        <i class="fas fa-check-circle"></i>
        <strong>نجح!</strong>
        <ul class="mb-0 mt-2">
            <?php foreach ($success as $msg): ?>
                <li><?php echo htmlspecialchars($msg); ?></li>
            <?php endforeach; ?>
        </ul>
    </div>
<?php endif; ?>

<form method="POST">
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0"><i class="fas fa-globe"></i> إعدادات الموقع</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="app_url" class="form-label">رابط الموقع *</label>
                    <input type="url" class="form-control" id="app_url" name="app_url" 
                           value="<?php echo htmlspecialchars($_POST['app_url'] ?? 'https://'); ?>" required>
                    <small class="form-text text-muted">مثال: https://yourdomain.com</small>
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="business_name" class="form-label">اسم الشركة *</label>
                    <input type="text" class="form-control" id="business_name" name="business_name" 
                           value="<?php echo htmlspecialchars($_POST['business_name'] ?? ''); ?>" required>
                </div>
            </div>
        </div>
    </div>
    
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0"><i class="fab fa-whatsapp"></i> إعدادات WhatsApp Business API</h5>
        </div>
        <div class="card-body">
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i>
                <strong>ملاحظة:</strong> يمكنك ترك هذه الحقول فارغة الآن وإدخالها لاحقاً من لوحة التحكم.
                <a href="https://developers.facebook.com/docs/whatsapp/cloud-api/get-started" target="_blank" class="alert-link">
                    دليل الحصول على WhatsApp API
                </a>
            </div>
            
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="whatsapp_token" class="form-label">Access Token</label>
                    <input type="text" class="form-control" id="whatsapp_token" name="whatsapp_token" 
                           value="<?php echo htmlspecialchars($_POST['whatsapp_token'] ?? ''); ?>">
                    <small class="form-text text-muted">من Facebook Developers Console</small>
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="whatsapp_phone_id" class="form-label">Phone Number ID</label>
                    <input type="text" class="form-control" id="whatsapp_phone_id" name="whatsapp_phone_id" 
                           value="<?php echo htmlspecialchars($_POST['whatsapp_phone_id'] ?? ''); ?>">
                    <small class="form-text text-muted">معرف رقم الهاتف في WhatsApp Business</small>
                </div>
            </div>
            
            <div class="mb-3">
                <label for="webhook_token" class="form-label">Webhook Verify Token</label>
                <input type="text" class="form-control" id="webhook_token" name="webhook_token" 
                       value="<?php echo htmlspecialchars($_POST['webhook_token'] ?? ''); ?>">
                <small class="form-text text-muted">سيتم إنشاؤه تلقائياً إذا تُرك فارغاً</small>
            </div>
            
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle"></i>
                <strong>مهم:</strong> رابط Webhook الخاص بك هو:
                <code id="webhook_url"><?php echo htmlspecialchars($_POST['app_url'] ?? 'https://yourdomain.com'); ?>/webhook.php</code>
                <br>
                <small>استخدم هذا الرابط في إعدادات WhatsApp Business API</small>
            </div>
        </div>
    </div>
    
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0"><i class="fas fa-clock"></i> إعدادات العمل</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-4 mb-3">
                    <label for="timezone" class="form-label">المنطقة الزمنية</label>
                    <select class="form-select" id="timezone" name="timezone">
                        <option value="Asia/Riyadh" <?php echo ($_POST['timezone'] ?? 'Asia/Riyadh') == 'Asia/Riyadh' ? 'selected' : ''; ?>>الرياض (GMT+3)</option>
                        <option value="Asia/Dubai" <?php echo ($_POST['timezone'] ?? '') == 'Asia/Dubai' ? 'selected' : ''; ?>>دبي (GMT+4)</option>
                        <option value="Africa/Cairo" <?php echo ($_POST['timezone'] ?? '') == 'Africa/Cairo' ? 'selected' : ''; ?>>القاهرة (GMT+2)</option>
                        <option value="Asia/Baghdad" <?php echo ($_POST['timezone'] ?? '') == 'Asia/Baghdad' ? 'selected' : ''; ?>>بغداد (GMT+3)</option>
                        <option value="Asia/Kuwait" <?php echo ($_POST['timezone'] ?? '') == 'Asia/Kuwait' ? 'selected' : ''; ?>>الكويت (GMT+3)</option>
                    </select>
                </div>
                
                <div class="col-md-4 mb-3">
                    <label for="business_hours_start" class="form-label">بداية ساعات العمل</label>
                    <input type="time" class="form-control" id="business_hours_start" name="business_hours_start" 
                           value="<?php echo htmlspecialchars($_POST['business_hours_start'] ?? '09:00'); ?>">
                </div>
                
                <div class="col-md-4 mb-3">
                    <label for="business_hours_end" class="form-label">نهاية ساعات العمل</label>
                    <input type="time" class="form-control" id="business_hours_end" name="business_hours_end" 
                           value="<?php echo htmlspecialchars($_POST['business_hours_end'] ?? '18:00'); ?>">
                </div>
            </div>
        </div>
    </div>
    
    <div class="d-flex justify-content-between">
        <a href="?step=2" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i> السابق
        </a>
        
        <button type="submit" class="btn btn-success btn-install">
            <i class="fas fa-save"></i> حفظ الإعدادات والمتابعة
        </button>
    </div>
</form>

<div class="progress mt-4">
    <div class="progress-bar" role="progressbar" style="width: 60%" aria-valuenow="60" aria-valuemin="0" aria-valuemax="100"></div>
</div>

<script>
// تحديث رابط Webhook تلقائياً
document.getElementById('app_url').addEventListener('input', function() {
    const webhookUrl = this.value.replace(/\/$/, '') + '/webhook.php';
    document.getElementById('webhook_url').textContent = webhookUrl;
});

// إنشاء رمز Webhook عشوائي
function generateWebhookToken() {
    const token = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
    document.getElementById('webhook_token').value = token;
}

// إضافة زر إنشاء رمز
document.addEventListener('DOMContentLoaded', function() {
    const webhookInput = document.getElementById('webhook_token');
    const generateButton = document.createElement('button');
    generateButton.type = 'button';
    generateButton.className = 'btn btn-outline-secondary btn-sm mt-1';
    generateButton.innerHTML = '<i class="fas fa-random"></i> إنشاء رمز عشوائي';
    generateButton.onclick = generateWebhookToken;
    
    webhookInput.parentNode.appendChild(generateButton);
});
</script>
