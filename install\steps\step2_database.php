<?php
/**
 * الخطوة الثانية: إعداد قاعدة البيانات
 * Step 2: Database Setup
 */

$errors = [];
$success = [];

if ($_POST) {
    $db_host = trim($_POST['db_host'] ?? 'localhost');
    $db_name = trim($_POST['db_name'] ?? 'whatsapp_automation');
    $db_username = trim($_POST['db_username'] ?? '');
    $db_password = $_POST['db_password'] ?? '';
    $create_database = isset($_POST['create_database']);
    
    // التحقق من البيانات المطلوبة
    if (empty($db_username)) {
        $errors[] = 'اسم المستخدم مطلوب';
    }
    
    if (empty($errors)) {
        try {
            // محاولة الاتصال بقاعدة البيانات
            if ($create_database) {
                // الاتصال بدون تحديد قاعدة بيانات لإنشائها
                $pdo = new PDO("mysql:host={$db_host};charset=utf8mb4", $db_username, $db_password, [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
                ]);
                
                // إنشاء قاعدة البيانات
                $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$db_name}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
                $success[] = 'تم إنشاء قاعدة البيانات بنجاح';
            }
            
            // الاتصال بقاعدة البيانات
            $pdo = new PDO("mysql:host={$db_host};dbname={$db_name};charset=utf8mb4", $db_username, $db_password, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
            ]);
            
            // قراءة ملف SQL وتنفيذه
            $sqlFile = '../database/whatsapp_automation.sql';
            if (file_exists($sqlFile)) {
                $sql = file_get_contents($sqlFile);
                
                // تقسيم الاستعلامات
                $statements = array_filter(array_map('trim', explode(';', $sql)));
                
                foreach ($statements as $statement) {
                    if (!empty($statement) && !preg_match('/^(--|\/\*|\*)/', $statement)) {
                        $pdo->exec($statement);
                    }
                }
                
                $success[] = 'تم إنشاء جداول قاعدة البيانات بنجاح';
                
                // حفظ بيانات قاعدة البيانات في الجلسة
                $_SESSION['db_config'] = [
                    'host' => $db_host,
                    'name' => $db_name,
                    'username' => $db_username,
                    'password' => $db_password
                ];
                
                // إعادة توجيه للخطوة التالية
                header('Location: ?step=3');
                exit;
                
            } else {
                $errors[] = 'ملف قاعدة البيانات غير موجود';
            }
            
        } catch (PDOException $e) {
            $errors[] = 'خطأ في قاعدة البيانات: ' . $e->getMessage();
        }
    }
}
?>

<h3 class="mb-4">
    <i class="fas fa-database text-primary"></i>
    إعداد قاعدة البيانات
</h3>

<p class="text-muted mb-4">
    أدخل بيانات الاتصال بقاعدة البيانات MySQL. سيتم إنشاء الجداول المطلوبة تلقائياً.
</p>

<?php if (!empty($errors)): ?>
    <div class="alert alert-danger">
        <i class="fas fa-exclamation-triangle"></i>
        <strong>خطأ!</strong>
        <ul class="mb-0 mt-2">
            <?php foreach ($errors as $error): ?>
                <li><?php echo htmlspecialchars($error); ?></li>
            <?php endforeach; ?>
        </ul>
    </div>
<?php endif; ?>

<?php if (!empty($success)): ?>
    <div class="alert alert-success">
        <i class="fas fa-check-circle"></i>
        <strong>نجح!</strong>
        <ul class="mb-0 mt-2">
            <?php foreach ($success as $msg): ?>
                <li><?php echo htmlspecialchars($msg); ?></li>
            <?php endforeach; ?>
        </ul>
    </div>
<?php endif; ?>

<form method="POST">
    <div class="row">
        <div class="col-md-6 mb-3">
            <label for="db_host" class="form-label">
                <i class="fas fa-server"></i> عنوان الخادم
            </label>
            <input type="text" class="form-control" id="db_host" name="db_host" 
                   value="<?php echo htmlspecialchars($_POST['db_host'] ?? 'localhost'); ?>" required>
            <small class="form-text text-muted">عادة localhost أو 127.0.0.1</small>
        </div>
        
        <div class="col-md-6 mb-3">
            <label for="db_name" class="form-label">
                <i class="fas fa-database"></i> اسم قاعدة البيانات
            </label>
            <input type="text" class="form-control" id="db_name" name="db_name" 
                   value="<?php echo htmlspecialchars($_POST['db_name'] ?? 'whatsapp_automation'); ?>" required>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-6 mb-3">
            <label for="db_username" class="form-label">
                <i class="fas fa-user"></i> اسم المستخدم
            </label>
            <input type="text" class="form-control" id="db_username" name="db_username" 
                   value="<?php echo htmlspecialchars($_POST['db_username'] ?? ''); ?>" required>
        </div>
        
        <div class="col-md-6 mb-3">
            <label for="db_password" class="form-label">
                <i class="fas fa-lock"></i> كلمة المرور
            </label>
            <input type="password" class="form-control" id="db_password" name="db_password" 
                   value="<?php echo htmlspecialchars($_POST['db_password'] ?? ''); ?>">
            <small class="form-text text-muted">اتركها فارغة إذا لم تكن هناك كلمة مرور</small>
        </div>
    </div>
    
    <div class="mb-4">
        <div class="form-check">
            <input class="form-check-input" type="checkbox" id="create_database" name="create_database" 
                   <?php echo isset($_POST['create_database']) ? 'checked' : ''; ?>>
            <label class="form-check-label" for="create_database">
                إنشاء قاعدة البيانات إذا لم تكن موجودة
            </label>
            <small class="form-text text-muted d-block">
                تأكد من أن المستخدم لديه صلاحيات إنشاء قواعد البيانات
            </small>
        </div>
    </div>
    
    <div class="alert alert-info">
        <i class="fas fa-info-circle"></i>
        <strong>ملاحظة:</strong> سيتم إنشاء الجداول التالية في قاعدة البيانات:
        <ul class="mb-0 mt-2">
            <li>system_settings - إعدادات النظام</li>
            <li>users - المستخدمين</li>
            <li>contacts - جهات الاتصال</li>
            <li>messages - الرسائل</li>
            <li>auto_replies - الردود التلقائية</li>
            <li>scheduled_messages - الرسائل المجدولة</li>
            <li>campaigns - الحملات التسويقية</li>
            <li>button_interactions - تفاعلات الأزرار</li>
            <li>customer_ratings - تقييمات العملاء</li>
            <li>daily_stats - الإحصائيات اليومية</li>
        </ul>
    </div>
    
    <div class="d-flex justify-content-between">
        <a href="?step=1" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i> السابق
        </a>
        
        <button type="submit" class="btn btn-success btn-install">
            <i class="fas fa-database"></i> إنشاء قاعدة البيانات والمتابعة
        </button>
    </div>
</form>

<div class="progress mt-4">
    <div class="progress-bar" role="progressbar" style="width: 40%" aria-valuenow="40" aria-valuemin="0" aria-valuemax="100"></div>
</div>

<script>
// اختبار الاتصال بقاعدة البيانات
function testConnection() {
    const formData = new FormData();
    formData.append('action', 'test_connection');
    formData.append('db_host', document.getElementById('db_host').value);
    formData.append('db_name', document.getElementById('db_name').value);
    formData.append('db_username', document.getElementById('db_username').value);
    formData.append('db_password', document.getElementById('db_password').value);
    
    fetch('ajax/test_database.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('تم الاتصال بقاعدة البيانات بنجاح!');
        } else {
            alert('فشل الاتصال: ' + data.error);
        }
    })
    .catch(error => {
        alert('خطأ في الاختبار: ' + error);
    });
}

// إضافة زر اختبار الاتصال
document.addEventListener('DOMContentLoaded', function() {
    const submitButton = document.querySelector('button[type="submit"]');
    const testButton = document.createElement('button');
    testButton.type = 'button';
    testButton.className = 'btn btn-outline-primary me-2';
    testButton.innerHTML = '<i class="fas fa-plug"></i> اختبار الاتصال';
    testButton.onclick = testConnection;
    
    submitButton.parentNode.insertBefore(testButton, submitButton);
});
</script>
