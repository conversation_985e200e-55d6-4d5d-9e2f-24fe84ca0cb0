-- قاعدة بيانات نظام أتمتة WhatsApp Business
-- WhatsApp Business Automation Database

CREATE DATABASE IF NOT EXISTS whatsapp_automation 
CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE whatsapp_automation;

-- جدول إعدادات النظام
CREATE TABLE IF NOT EXISTS system_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- جدول المستخدمين والإدارة
CREATE TABLE IF NOT EXISTS users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(100) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(100),
    role ENUM('admin', 'manager', 'operator') DEFAULT 'operator',
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- جدول جهات الاتصال
CREATE TABLE IF NOT EXISTS contacts (
    id INT PRIMARY KEY AUTO_INCREMENT,
    phone_number VARCHAR(20) NOT NULL UNIQUE,
    whatsapp_id VARCHAR(50),
    name VARCHAR(100),
    email VARCHAR(100),
    company VARCHAR(100),
    notes TEXT,
    tags JSON,
    is_blocked BOOLEAN DEFAULT FALSE,
    last_message_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_phone (phone_number),
    INDEX idx_whatsapp_id (whatsapp_id),
    INDEX idx_name (name)
);

-- جدول المجموعات
CREATE TABLE IF NOT EXISTS groups (
    id INT PRIMARY KEY AUTO_INCREMENT,
    group_id VARCHAR(100) NOT NULL UNIQUE,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    member_count INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- جدول الرسائل
CREATE TABLE IF NOT EXISTS messages (
    id INT PRIMARY KEY AUTO_INCREMENT,
    message_id VARCHAR(100),
    contact_id INT,
    group_id INT NULL,
    direction ENUM('incoming', 'outgoing') NOT NULL,
    message_type ENUM('text', 'image', 'document', 'audio', 'video', 'location', 'contact') DEFAULT 'text',
    content TEXT,
    media_url VARCHAR(500),
    media_filename VARCHAR(255),
    status ENUM('sent', 'delivered', 'read', 'failed') DEFAULT 'sent',
    is_automated BOOLEAN DEFAULT FALSE,
    template_id INT NULL,
    scheduled_at TIMESTAMP NULL,
    sent_at TIMESTAMP NULL,
    delivered_at TIMESTAMP NULL,
    read_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (contact_id) REFERENCES contacts(id) ON DELETE CASCADE,
    FOREIGN KEY (group_id) REFERENCES groups(id) ON DELETE SET NULL,
    INDEX idx_contact_id (contact_id),
    INDEX idx_direction (direction),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    INDEX idx_scheduled_at (scheduled_at)
);

-- جدول قوالب الرسائل
CREATE TABLE IF NOT EXISTS message_templates (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    content TEXT NOT NULL,
    variables JSON,
    category ENUM('welcome', 'follow_up', 'promotion', 'support', 'custom') DEFAULT 'custom',
    is_active BOOLEAN DEFAULT TRUE,
    usage_count INT DEFAULT 0,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
);

-- جدول الردود التلقائية
CREATE TABLE IF NOT EXISTS auto_replies (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    keywords JSON NOT NULL,
    response_text TEXT NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    priority INT DEFAULT 1,
    conditions JSON,
    working_hours_only BOOLEAN DEFAULT FALSE,
    usage_count INT DEFAULT 0,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_active (is_active),
    INDEX idx_priority (priority)
);

-- جدول الرسائل المجدولة
CREATE TABLE IF NOT EXISTS scheduled_messages (
    id INT PRIMARY KEY AUTO_INCREMENT,
    contact_id INT,
    group_id INT NULL,
    message_content TEXT NOT NULL,
    media_url VARCHAR(500),
    scheduled_time TIMESTAMP NOT NULL,
    status ENUM('pending', 'sent', 'failed', 'cancelled') DEFAULT 'pending',
    repeat_type ENUM('none', 'daily', 'weekly', 'monthly') DEFAULT 'none',
    repeat_until DATE NULL,
    created_by INT,
    sent_at TIMESTAMP NULL,
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (contact_id) REFERENCES contacts(id) ON DELETE CASCADE,
    FOREIGN KEY (group_id) REFERENCES groups(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_scheduled_time (scheduled_time),
    INDEX idx_status (status)
);

-- جدول الحملات التسويقية
CREATE TABLE IF NOT EXISTS campaigns (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    message_content TEXT NOT NULL,
    media_url VARCHAR(500),
    target_type ENUM('all', 'group', 'tags', 'custom') DEFAULT 'custom',
    target_criteria JSON,
    status ENUM('draft', 'scheduled', 'running', 'completed', 'paused') DEFAULT 'draft',
    scheduled_at TIMESTAMP NULL,
    started_at TIMESTAMP NULL,
    completed_at TIMESTAMP NULL,
    total_recipients INT DEFAULT 0,
    sent_count INT DEFAULT 0,
    delivered_count INT DEFAULT 0,
    read_count INT DEFAULT 0,
    failed_count INT DEFAULT 0,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
);

-- جدول سجل الأنشطة
CREATE TABLE IF NOT EXISTS activity_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT,
    action VARCHAR(100) NOT NULL,
    entity_type VARCHAR(50),
    entity_id INT,
    details JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_action (action),
    INDEX idx_created_at (created_at)
);

-- جدول الإحصائيات اليومية
CREATE TABLE IF NOT EXISTS daily_stats (
    id INT PRIMARY KEY AUTO_INCREMENT,
    date DATE NOT NULL UNIQUE,
    messages_sent INT DEFAULT 0,
    messages_received INT DEFAULT 0,
    auto_replies_sent INT DEFAULT 0,
    new_contacts INT DEFAULT 0,
    active_contacts INT DEFAULT 0,
    campaigns_sent INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_date (date)
);

-- جدول تفاعلات الأزرار
CREATE TABLE IF NOT EXISTS button_interactions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    contact_id INT,
    button_id VARCHAR(100) NOT NULL,
    button_title VARCHAR(200),
    interaction_type ENUM('button_click', 'list_selection') DEFAULT 'button_click',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (contact_id) REFERENCES contacts(id) ON DELETE CASCADE,
    INDEX idx_contact_id (contact_id),
    INDEX idx_button_id (button_id),
    INDEX idx_created_at (created_at)
);

-- جدول تقييمات العملاء
CREATE TABLE IF NOT EXISTS customer_ratings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    contact_id INT,
    rating INT NOT NULL CHECK (rating >= 1 AND rating <= 5),
    source ENUM('interactive_button', 'text_message', 'manual') DEFAULT 'text_message',
    feedback_text TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (contact_id) REFERENCES contacts(id) ON DELETE CASCADE,
    INDEX idx_contact_id (contact_id),
    INDEX idx_rating (rating),
    INDEX idx_created_at (created_at)
);

-- جدول إشعارات الدعم
CREATE TABLE IF NOT EXISTS support_notifications (
    id INT PRIMARY KEY AUTO_INCREMENT,
    phone_number VARCHAR(20) NOT NULL,
    type VARCHAR(50) NOT NULL,
    data JSON,
    status ENUM('pending', 'assigned', 'in_progress', 'resolved') DEFAULT 'pending',
    assigned_to INT NULL,
    resolved_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (assigned_to) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_phone_number (phone_number),
    INDEX idx_type (type),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);

-- جدول قوالب الأزرار التفاعلية
CREATE TABLE IF NOT EXISTS interactive_templates (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    type ENUM('buttons', 'list') NOT NULL,
    header_text VARCHAR(60),
    body_text TEXT NOT NULL,
    footer_text VARCHAR(60),
    buttons_data JSON,
    is_active BOOLEAN DEFAULT TRUE,
    usage_count INT DEFAULT 0,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_type (type),
    INDEX idx_active (is_active)
);

-- إدراج البيانات الأولية
INSERT INTO system_settings (setting_key, setting_value, description) VALUES
('business_name', 'شركتي', 'اسم الشركة أو النشاط التجاري'),
('business_hours_start', '09:00', 'بداية ساعات العمل'),
('business_hours_end', '18:00', 'نهاية ساعات العمل'),
('timezone', 'Asia/Riyadh', 'المنطقة الزمنية'),
('welcome_message', 'مرحباً بك! شكراً لتواصلك معنا. سنرد عليك في أقرب وقت ممكن.', 'رسالة الترحيب التلقائية'),
('out_of_hours_message', 'شكراً لتواصلك معنا. ساعات العمل من 9 صباحاً إلى 6 مساءً. سنرد عليك خلال ساعات العمل.', 'رسالة خارج ساعات العمل'),
('auto_reply_enabled', '1', 'تفعيل الردود التلقائية'),
('max_messages_per_minute', '30', 'الحد الأقصى للرسائل في الدقيقة'),
('whatsapp_api_token', '', 'رمز WhatsApp Business API'),
('whatsapp_phone_number_id', '', 'معرف رقم الهاتف في WhatsApp Business');

-- إنشاء مستخدم إداري افتراضي
-- كلمة المرور: admin123
INSERT INTO users (username, email, password_hash, full_name, role) VALUES
('admin', '<EMAIL>', '$2y$10$TKh8H1.PfQx37YgCzwiKb.KjNyWgaHb9cbcoQgdIVFlYg7B77UdFm', 'المدير العام', 'admin');
