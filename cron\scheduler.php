<?php
/**
 * مجدول المهام التلقائية
 * Automated Tasks Scheduler
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../classes/WhatsAppAPI.php';
require_once __DIR__ . '/../classes/AutomationEngine.php';

// التأكد من تشغيل المجدول من سطر الأوامر فقط
if (php_sapi_name() !== 'cli') {
    die('هذا السكريبت يجب تشغيله من سطر الأوامر فقط');
}

// إنشاء مثيلات الفئات المطلوبة
$whatsappAPI = new WhatsAppAPI($database);
$automationEngine = new AutomationEngine($database, $whatsappAPI);

echo "[" . date('Y-m-d H:i:s') . "] بدء تشغيل مجدول المهام\n";

try {
    // 1. معالجة الرسائل المجدولة
    echo "معالجة الرسائل المجدولة...\n";
    $processedMessages = $automationEngine->processScheduledMessages();
    echo "تم معالجة {$processedMessages} رسالة مجدولة\n";
    
    // 2. معالجة الحملات التسويقية المجدولة
    echo "معالجة الحملات التسويقية...\n";
    $processedCampaigns = processCampaigns();
    echo "تم معالجة {$processedCampaigns} حملة\n";
    
    // 3. تنظيف البيانات القديمة
    echo "تنظيف البيانات القديمة...\n";
    cleanupOldData();
    echo "تم تنظيف البيانات القديمة\n";
    
    // 4. تحديث الإحصائيات اليومية
    echo "تحديث الإحصائيات اليومية...\n";
    updateDailyStats();
    echo "تم تحديث الإحصائيات\n";
    
    // 5. إنشاء نسخة احتياطية (يومياً في الساعة 2 صباحاً)
    if (date('H') == '02') {
        echo "إنشاء نسخة احتياطية...\n";
        createBackup();
        echo "تم إنشاء النسخة الاحتياطية\n";
    }
    
    echo "[" . date('Y-m-d H:i:s') . "] انتهاء تشغيل مجدول المهام بنجاح\n";
    
} catch (Exception $e) {
    echo "[" . date('Y-m-d H:i:s') . "] خطأ في مجدول المهام: " . $e->getMessage() . "\n";
    logError("Scheduler error: " . $e->getMessage());
}

/**
 * معالجة الحملات التسويقية
 * Process Marketing Campaigns
 */
function processCampaigns() {
    global $database, $whatsappAPI;
    
    $campaigns = $database->fetchAll("
        SELECT * FROM campaigns 
        WHERE status = 'scheduled' 
        AND scheduled_at <= NOW()
        ORDER BY scheduled_at ASC
        LIMIT 5
    ");
    
    $processedCount = 0;
    
    foreach ($campaigns as $campaign) {
        try {
            // تحديث حالة الحملة إلى قيد التشغيل
            $database->update(
                'campaigns',
                [
                    'status' => 'running',
                    'started_at' => date('Y-m-d H:i:s')
                ],
                'id = ?',
                [$campaign['id']]
            );
            
            // الحصول على قائمة المستلمين
            $recipients = getRecipients($campaign);
            
            $sentCount = 0;
            $deliveredCount = 0;
            $failedCount = 0;
            
            foreach ($recipients as $recipient) {
                try {
                    $messageId = $whatsappAPI->sendTextMessage(
                        $recipient['phone_number'],
                        $campaign['message_content'],
                        false
                    );
                    
                    if ($messageId) {
                        $sentCount++;
                        $deliveredCount++; // سيتم تحديثها لاحقاً من webhook
                    } else {
                        $failedCount++;
                    }
                    
                    // تأخير لتجنب تجاوز حدود API
                    sleep(2);
                    
                } catch (Exception $e) {
                    $failedCount++;
                    logError("Campaign message failed: " . $e->getMessage());
                }
            }
            
            // تحديث إحصائيات الحملة
            $database->update(
                'campaigns',
                [
                    'status' => 'completed',
                    'completed_at' => date('Y-m-d H:i:s'),
                    'sent_count' => $sentCount,
                    'delivered_count' => $deliveredCount,
                    'failed_count' => $failedCount
                ],
                'id = ?',
                [$campaign['id']]
            );
            
            $processedCount++;
            
        } catch (Exception $e) {
            // تحديث حالة الحملة إلى فاشلة
            $database->update(
                'campaigns',
                ['status' => 'failed'],
                'id = ?',
                [$campaign['id']]
            );
            
            logError("Campaign processing failed: " . $e->getMessage());
        }
    }
    
    return $processedCount;
}

/**
 * الحصول على قائمة المستلمين للحملة
 * Get Campaign Recipients
 */
function getRecipients($campaign) {
    global $database;
    
    $targetCriteria = json_decode($campaign['target_criteria'], true);
    
    switch ($campaign['target_type']) {
        case 'all':
            return $database->fetchAll("
                SELECT phone_number FROM contacts 
                WHERE is_blocked = 0
            ");
            
        case 'tags':
            $tags = $targetCriteria['tags'] ?? [];
            $tagConditions = implode(',', array_fill(0, count($tags), '?'));
            return $database->fetchAll("
                SELECT phone_number FROM contacts 
                WHERE is_blocked = 0 
                AND JSON_OVERLAPS(tags, JSON_ARRAY(" . implode(',', array_map(function($tag) { return '"' . $tag . '"'; }, $tags)) . "))
            ");
            
        case 'custom':
            // تنفيذ معايير مخصصة
            $whereClause = "is_blocked = 0";
            $params = [];
            
            if (isset($targetCriteria['last_message_days'])) {
                $whereClause .= " AND last_message_at >= DATE_SUB(NOW(), INTERVAL ? DAY)";
                $params[] = $targetCriteria['last_message_days'];
            }
            
            return $database->fetchAll("
                SELECT phone_number FROM contacts 
                WHERE {$whereClause}
            ", $params);
            
        default:
            return [];
    }
}

/**
 * تنظيف البيانات القديمة
 * Clean Up Old Data
 */
function cleanupOldData() {
    global $database;
    
    // حذف الرسائل القديمة (أكثر من 6 أشهر)
    $database->query("
        DELETE FROM messages 
        WHERE created_at < DATE_SUB(NOW(), INTERVAL 6 MONTH)
    ");
    
    // حذف سجلات النشاط القديمة (أكثر من 3 أشهر)
    $database->query("
        DELETE FROM activity_logs 
        WHERE created_at < DATE_SUB(NOW(), INTERVAL 3 MONTH)
    ");
    
    // حذف الرسائل المجدولة المكتملة القديمة (أكثر من شهر)
    $database->query("
        DELETE FROM scheduled_messages 
        WHERE status IN ('sent', 'failed') 
        AND created_at < DATE_SUB(NOW(), INTERVAL 1 MONTH)
    ");
    
    // حذف ملفات السجلات القديمة
    $logFiles = glob(LOG_PATH . '*.log');
    foreach ($logFiles as $file) {
        if (filemtime($file) < strtotime('-30 days')) {
            unlink($file);
        }
    }
}

/**
 * تحديث الإحصائيات اليومية
 * Update Daily Statistics
 */
function updateDailyStats() {
    global $database;
    
    $today = date('Y-m-d');
    
    // جلب إحصائيات اليوم
    $stats = $database->fetchOne("
        SELECT 
            SUM(CASE WHEN direction = 'outgoing' THEN 1 ELSE 0 END) as messages_sent,
            SUM(CASE WHEN direction = 'incoming' THEN 1 ELSE 0 END) as messages_received,
            SUM(CASE WHEN is_automated = 1 THEN 1 ELSE 0 END) as auto_replies_sent
        FROM messages 
        WHERE DATE(created_at) = ?
    ", [$today]);
    
    $newContacts = $database->fetchOne("
        SELECT COUNT(*) as count 
        FROM contacts 
        WHERE DATE(created_at) = ?
    ", [$today])['count'];
    
    $activeContacts = $database->fetchOne("
        SELECT COUNT(DISTINCT contact_id) as count 
        FROM messages 
        WHERE DATE(created_at) = ?
    ", [$today])['count'];
    
    $campaignsSent = $database->fetchOne("
        SELECT COUNT(*) as count 
        FROM campaigns 
        WHERE DATE(completed_at) = ? AND status = 'completed'
    ", [$today])['count'];
    
    // تحديث أو إدراج الإحصائيات
    $existing = $database->fetchOne("
        SELECT id FROM daily_stats WHERE date = ?
    ", [$today]);
    
    $statsData = [
        'messages_sent' => $stats['messages_sent'] ?? 0,
        'messages_received' => $stats['messages_received'] ?? 0,
        'auto_replies_sent' => $stats['auto_replies_sent'] ?? 0,
        'new_contacts' => $newContacts,
        'active_contacts' => $activeContacts,
        'campaigns_sent' => $campaignsSent,
        'updated_at' => date('Y-m-d H:i:s')
    ];
    
    if ($existing) {
        $database->update('daily_stats', $statsData, 'date = ?', [$today]);
    } else {
        $statsData['date'] = $today;
        $database->insert('daily_stats', $statsData);
    }
}

/**
 * إنشاء نسخة احتياطية
 * Create Backup
 */
function createBackup() {
    $backupFile = BACKUP_PATH . 'backup_' . date('Y-m-d_H-i-s') . '.sql';
    
    $command = sprintf(
        'mysqldump --host=%s --user=%s --password=%s %s > %s',
        DB_HOST,
        DB_USERNAME,
        DB_PASSWORD,
        DB_NAME,
        $backupFile
    );
    
    exec($command, $output, $returnCode);
    
    if ($returnCode === 0) {
        // ضغط النسخة الاحتياطية
        $compressedFile = $backupFile . '.gz';
        exec("gzip {$backupFile}", $output, $returnCode);
        
        // حذف النسخ الاحتياطية القديمة
        $oldBackups = glob(BACKUP_PATH . 'backup_*.sql.gz');
        foreach ($oldBackups as $backup) {
            if (filemtime($backup) < strtotime('-' . BACKUP_RETENTION_DAYS . ' days')) {
                unlink($backup);
            }
        }
        
        echo "تم إنشاء النسخة الاحتياطية: " . basename($compressedFile) . "\n";
    } else {
        throw new Exception("فشل في إنشاء النسخة الاحتياطية");
    }
}

/**
 * تسجيل الأخطاء
 * Log Errors
 */
function logError($message) {
    $logFile = LOG_PATH . 'scheduler_' . date('Y-m-d') . '.log';
    $timestamp = date('Y-m-d H:i:s');
    file_put_contents($logFile, "[$timestamp] ERROR: $message\n", FILE_APPEND | LOCK_EX);
}
?>
