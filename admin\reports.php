<?php
/**
 * صفحة التقارير
 * Reports Page
 */

session_start();
require_once '../config/config.php';
require_once '../classes/ReportsManager.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

$reportsManager = new ReportsManager($database);

// معالجة طلبات التصدير
if (isset($_POST['export'])) {
    $reportType = $_POST['report_type'];
    $startDate = $_POST['start_date'] ?? null;
    $endDate = $_POST['end_date'] ?? null;
    $format = $_POST['format'] ?? 'csv';
    
    switch ($reportType) {
        case 'messages':
            $reportData = $reportsManager->getMessagesReport($startDate, $endDate);
            break;
        case 'contacts':
            $reportData = $reportsManager->getContactsReport($startDate, $endDate);
            break;
        case 'auto_replies':
            $reportData = $reportsManager->getAutoRepliesReport($startDate, $endDate);
            break;
        case 'performance':
            $reportData = $reportsManager->getPerformanceReport($startDate, $endDate);
            break;
        case 'campaigns':
            $reportData = $reportsManager->getCampaignsReport($startDate, $endDate);
            break;
    }
    
    if ($format === 'csv') {
        $filename = $reportsManager->exportToCSV($reportData, $reportType);
        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment; filename="' . basename($filename) . '"');
        readfile($filename);
        unlink($filename);
        exit;
    }
}

// جلب البيانات للعرض
$startDate = $_GET['start_date'] ?? date('Y-m-01');
$endDate = $_GET['end_date'] ?? date('Y-m-d');

$messagesReport = $reportsManager->getMessagesReport($startDate, $endDate);
$contactsReport = $reportsManager->getContactsReport($startDate, $endDate);
$autoRepliesReport = $reportsManager->getAutoRepliesReport($startDate, $endDate);
$performanceReport = $reportsManager->getPerformanceReport($startDate, $endDate);
$campaignsReport = $reportsManager->getCampaignsReport($startDate, $endDate);

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التقارير - نظام أتمتة WhatsApp Business</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/admin.css" rel="stylesheet">
</head>
<body>
    <?php include 'includes/navbar.php'; ?>
    
    <div class="container-fluid">
        <div class="row">
            <?php include 'includes/sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">التقارير والإحصائيات</h1>
                </div>

                <!-- فلاتر التاريخ -->
                <div class="card mb-4">
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-4">
                                <label for="start_date" class="form-label">من تاريخ</label>
                                <input type="date" class="form-control" id="start_date" name="start_date" value="<?php echo $startDate; ?>">
                            </div>
                            <div class="col-md-4">
                                <label for="end_date" class="form-label">إلى تاريخ</label>
                                <input type="date" class="form-control" id="end_date" name="end_date" value="<?php echo $endDate; ?>">
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">&nbsp;</label>
                                <div>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search"></i> تطبيق الفلتر
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- تقرير الرسائل -->
                <div class="card mb-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">تقرير الرسائل</h5>
                        <form method="POST" class="d-inline">
                            <input type="hidden" name="report_type" value="messages">
                            <input type="hidden" name="start_date" value="<?php echo $startDate; ?>">
                            <input type="hidden" name="end_date" value="<?php echo $endDate; ?>">
                            <button type="submit" name="export" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-download"></i> تصدير
                            </button>
                        </form>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h4 class="text-primary"><?php echo number_format($messagesReport['stats']['total_messages']); ?></h4>
                                    <p class="mb-0">إجمالي الرسائل</p>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h4 class="text-success"><?php echo number_format($messagesReport['stats']['sent_messages']); ?></h4>
                                    <p class="mb-0">رسائل مرسلة</p>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h4 class="text-info"><?php echo number_format($messagesReport['stats']['received_messages']); ?></h4>
                                    <p class="mb-0">رسائل مستقبلة</p>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h4 class="text-warning"><?php echo number_format($messagesReport['stats']['automated_messages']); ?></h4>
                                    <p class="mb-0">ردود تلقائية</p>
                                </div>
                            </div>
                        </div>
                        
                        <hr>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <h6>توزيع أنواع الرسائل</h6>
                                <canvas id="messageTypesChart"></canvas>
                            </div>
                            <div class="col-md-6">
                                <h6>الرسائل حسب الساعة</h6>
                                <canvas id="hourlyChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- تقرير جهات الاتصال -->
                <div class="card mb-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">تقرير جهات الاتصال</h5>
                        <form method="POST" class="d-inline">
                            <input type="hidden" name="report_type" value="contacts">
                            <input type="hidden" name="start_date" value="<?php echo $startDate; ?>">
                            <input type="hidden" name="end_date" value="<?php echo $endDate; ?>">
                            <button type="submit" name="export" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-download"></i> تصدير
                            </button>
                        </form>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h4 class="text-primary"><?php echo number_format($contactsReport['stats']['total_contacts']); ?></h4>
                                    <p class="mb-0">إجمالي جهات الاتصال</p>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h4 class="text-success"><?php echo number_format($contactsReport['stats']['active_contacts']); ?></h4>
                                    <p class="mb-0">نشط (7 أيام)</p>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h4 class="text-info"><?php echo number_format($contactsReport['stats']['monthly_active']); ?></h4>
                                    <p class="mb-0">نشط (30 يوم)</p>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h4 class="text-danger"><?php echo number_format($contactsReport['stats']['blocked_contacts']); ?></h4>
                                    <p class="mb-0">محظور</p>
                                </div>
                            </div>
                        </div>
                        
                        <hr>
                        
                        <h6>أكثر جهات الاتصال تفاعلاً</h6>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>الاسم</th>
                                        <th>رقم الهاتف</th>
                                        <th>عدد الرسائل</th>
                                        <th>آخر رسالة</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach (array_slice($contactsReport['top_contacts'], 0, 10) as $contact): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($contact['name'] ?? 'غير محدد'); ?></td>
                                        <td><?php echo htmlspecialchars($contact['phone_number']); ?></td>
                                        <td><?php echo number_format($contact['message_count']); ?></td>
                                        <td><?php echo $contact['last_message'] ? date('Y-m-d H:i', strtotime($contact['last_message'])) : '-'; ?></td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- تقرير الأداء -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">تقرير الأداء</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="text-center">
                                    <h4 class="text-success"><?php echo number_format($performanceReport['response_rate']['delivery_rate'] ?? 0, 1); ?>%</h4>
                                    <p class="mb-0">معدل التسليم</p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center">
                                    <h4 class="text-info"><?php echo number_format($performanceReport['response_rate']['read_rate'] ?? 0, 1); ?>%</h4>
                                    <p class="mb-0">معدل القراءة</p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center">
                                    <h4 class="text-primary"><?php echo number_format($performanceReport['response_time']['avg_response_time_minutes'] ?? 0, 1); ?></h4>
                                    <p class="mb-0">متوسط وقت الرد (دقيقة)</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- تقرير الردود التلقائية -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">تقرير الردود التلقائية</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="text-center">
                                    <h4 class="text-primary"><?php echo number_format($autoRepliesReport['stats']['total_auto_replies']); ?></h4>
                                    <p class="mb-0">إجمالي الردود</p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center">
                                    <h4 class="text-success"><?php echo number_format($autoRepliesReport['stats']['active_replies']); ?></h4>
                                    <p class="mb-0">ردود نشطة</p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center">
                                    <h4 class="text-info"><?php echo number_format($autoRepliesReport['stats']['total_usage']); ?></h4>
                                    <p class="mb-0">إجمالي الاستخدام</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // رسم بياني لأنواع الرسائل
        const messageTypesData = <?php echo json_encode($messagesReport['message_types']); ?>;
        const messageTypesChart = new Chart(document.getElementById('messageTypesChart'), {
            type: 'doughnut',
            data: {
                labels: messageTypesData.map(item => item.message_type),
                datasets: [{
                    data: messageTypesData.map(item => item.count),
                    backgroundColor: ['#007bff', '#28a745', '#ffc107', '#dc3545', '#6f42c1']
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        // رسم بياني للرسائل حسب الساعة
        const hourlyData = <?php echo json_encode($messagesReport['hourly_stats']); ?>;
        const hourlyChart = new Chart(document.getElementById('hourlyChart'), {
            type: 'bar',
            data: {
                labels: hourlyData.map(item => item.hour + ':00'),
                datasets: [{
                    label: 'عدد الرسائل',
                    data: hourlyData.map(item => item.count),
                    backgroundColor: '#007bff'
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    </script>
</body>
</html>
