<?php
/**
 * Webhook لاستقبال رسائل WhatsApp
 * WhatsApp Webhook Handler
 */

require_once 'config/config.php';
require_once 'classes/WhatsAppAPI.php';
require_once 'classes/MessageHandler.php';

// التحقق من طريقة الطلب
$method = $_SERVER['REQUEST_METHOD'];

if ($method === 'GET') {
    // التحقق من webhook
    verifyWebhook();
} elseif ($method === 'POST') {
    // معالجة الرسائل الواردة
    processIncomingWebhook();
} else {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
}

/**
 * التحقق من webhook
 * Verify webhook
 */
function verifyWebhook() {
    $verifyToken = $_GET['hub_verify_token'] ?? '';
    $challenge = $_GET['hub_challenge'] ?? '';
    $mode = $_GET['hub_mode'] ?? '';
    
    if ($mode === 'subscribe' && $verifyToken === WHATSAPP_WEBHOOK_VERIFY_TOKEN) {
        echo $challenge;
        http_response_code(200);
    } else {
        http_response_code(403);
        echo json_encode(['error' => 'Forbidden']);
    }
}

/**
 * معالجة webhook الوارد
 * Process incoming webhook
 */
function processIncomingWebhook() {
    global $database;
    
    try {
        // قراءة البيانات الواردة
        $input = file_get_contents('php://input');
        $data = json_decode($input, true);
        
        if (!$data) {
            http_response_code(400);
            echo json_encode(['error' => 'Invalid JSON']);
            return;
        }
        
        // تسجيل البيانات الواردة للتشخيص
        logWebhookData($input);
        
        // التحقق من وجود البيانات المطلوبة
        if (!isset($data['entry']) || !is_array($data['entry'])) {
            http_response_code(200);
            echo json_encode(['status' => 'ok']);
            return;
        }
        
        // إنشاء مثيلات الفئات المطلوبة
        $whatsappAPI = new WhatsAppAPI($database);
        $messageHandler = new MessageHandler($database, $whatsappAPI);
        
        // معالجة كل entry
        foreach ($data['entry'] as $entry) {
            if (!isset($entry['changes'])) continue;
            
            foreach ($entry['changes'] as $change) {
                if ($change['field'] !== 'messages') continue;
                
                $value = $change['value'];
                
                // معالجة الرسائل الواردة
                if (isset($value['messages'])) {
                    foreach ($value['messages'] as $message) {
                        // إضافة معلومات إضافية للرسائل التفاعلية
                        if (isset($message['interactive'])) {
                            logWebhookData("Interactive message received: " . json_encode($message['interactive']));
                        }

                        $messageHandler->processIncomingMessage($message);
                    }
                }
                
                // معالجة تحديثات حالة الرسائل
                if (isset($value['statuses'])) {
                    foreach ($value['statuses'] as $status) {
                        $messageHandler->processStatusUpdate($status);
                    }
                }
            }
        }
        
        http_response_code(200);
        echo json_encode(['status' => 'success']);
        
    } catch (Exception $e) {
        logError("Webhook processing error: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['error' => 'Internal server error']);
    }
}

/**
 * تسجيل بيانات webhook
 * Log webhook data
 */
function logWebhookData($data) {
    $logFile = LOG_PATH . 'webhook_' . date('Y-m-d') . '.log';
    $timestamp = date('Y-m-d H:i:s');
    $logEntry = "[$timestamp] WEBHOOK DATA: $data\n";
    file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
}

/**
 * تسجيل الأخطاء
 * Log errors
 */
function logError($message) {
    $logFile = LOG_PATH . 'webhook_errors_' . date('Y-m-d') . '.log';
    $timestamp = date('Y-m-d H:i:s');
    file_put_contents($logFile, "[$timestamp] ERROR: $message\n", FILE_APPEND | LOCK_EX);
}
?>
