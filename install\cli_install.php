<?php
/**
 * معالج التثبيت من سطر الأوامر
 * Command Line Installation Handler
 */

// التأكد من تشغيل السكريبت من سطر الأوامر
if (php_sapi_name() !== 'cli') {
    die('هذا السكريبت يجب تشغيله من سطر الأوامر فقط');
}

echo "🚀 معالج التثبيت التلقائي لنظام أتمتة WhatsApp Business\n";
echo str_repeat('=', 60) . "\n\n";

// فحص المتطلبات
echo "🔍 فحص المتطلبات...\n";

$requirements = [
    'PHP 8.0+' => version_compare(PHP_VERSION, '8.0.0', '>='),
    'PDO MySQL' => extension_loaded('pdo_mysql'),
    'cURL' => extension_loaded('curl'),
    'JSON' => extension_loaded('json'),
    'mbstring' => extension_loaded('mbstring'),
    'OpenSSL' => extension_loaded('openssl')
];

$allRequirementsMet = true;
foreach ($requirements as $requirement => $met) {
    $status = $met ? '✅' : '❌';
    echo "  $status $requirement\n";
    if (!$met) $allRequirementsMet = false;
}

if (!$allRequirementsMet) {
    echo "\n❌ بعض المتطلبات غير متوفرة. يرجى إصلاحها قبل المتابعة.\n";
    exit(1);
}

echo "\n✅ جميع المتطلبات متوفرة!\n\n";

// جمع معلومات قاعدة البيانات
echo "📊 إعداد قاعدة البيانات:\n";
$dbConfig = [];

$dbConfig['host'] = readline("عنوان خادم قاعدة البيانات [localhost]: ") ?: 'localhost';
$dbConfig['name'] = readline("اسم قاعدة البيانات [whatsapp_automation]: ") ?: 'whatsapp_automation';
$dbConfig['username'] = readline("اسم مستخدم قاعدة البيانات: ");
$dbConfig['password'] = readline("كلمة مرور قاعدة البيانات: ");

if (empty($dbConfig['username'])) {
    echo "❌ اسم المستخدم مطلوب\n";
    exit(1);
}

// اختبار الاتصال بقاعدة البيانات
echo "\n🔌 اختبار الاتصال بقاعدة البيانات...\n";

try {
    $createDb = readline("إنشاء قاعدة البيانات إذا لم تكن موجودة؟ [y/N]: ");
    
    if (strtolower($createDb) === 'y') {
        $pdo = new PDO(
            "mysql:host={$dbConfig['host']};charset=utf8mb4",
            $dbConfig['username'],
            $dbConfig['password'],
            [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
        );
        
        $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$dbConfig['name']}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        echo "✅ تم إنشاء قاعدة البيانات\n";
    }
    
    $pdo = new PDO(
        "mysql:host={$dbConfig['host']};dbname={$dbConfig['name']};charset=utf8mb4",
        $dbConfig['username'],
        $dbConfig['password'],
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );
    
    echo "✅ تم الاتصال بقاعدة البيانات بنجاح\n";
    
} catch (PDOException $e) {
    echo "❌ فشل الاتصال بقاعدة البيانات: " . $e->getMessage() . "\n";
    exit(1);
}

// إنشاء الجداول
echo "\n🏗️ إنشاء جداول قاعدة البيانات...\n";

$sqlFile = '../database/whatsapp_automation.sql';
if (!file_exists($sqlFile)) {
    echo "❌ ملف قاعدة البيانات غير موجود: $sqlFile\n";
    exit(1);
}

try {
    $sql = file_get_contents($sqlFile);
    $statements = array_filter(array_map('trim', explode(';', $sql)));
    
    foreach ($statements as $statement) {
        if (!empty($statement) && !preg_match('/^(--|\/\*|\*)/', $statement)) {
            $pdo->exec($statement);
        }
    }
    
    echo "✅ تم إنشاء جداول قاعدة البيانات بنجاح\n";
    
} catch (PDOException $e) {
    echo "❌ فشل في إنشاء الجداول: " . $e->getMessage() . "\n";
    exit(1);
}

// جمع إعدادات النظام
echo "\n⚙️ إعداد النظام:\n";

$appUrl = readline("رابط الموقع (مثال: https://yourdomain.com): ");
$businessName = readline("اسم الشركة: ");
$timezone = readline("المنطقة الزمنية [Asia/Riyadh]: ") ?: 'Asia/Riyadh';

if (empty($appUrl) || empty($businessName)) {
    echo "❌ رابط الموقع واسم الشركة مطلوبان\n";
    exit(1);
}

// إعدادات WhatsApp (اختيارية)
echo "\n📱 إعدادات WhatsApp Business API (يمكن تركها فارغة):\n";
$whatsappToken = readline("WhatsApp Access Token: ");
$whatsappPhoneId = readline("WhatsApp Phone Number ID: ");
$webhookToken = readline("Webhook Verify Token (سيتم إنشاؤه تلقائياً إذا تُرك فارغاً): ");

if (empty($webhookToken)) {
    $webhookToken = bin2hex(random_bytes(16));
    echo "✅ تم إنشاء Webhook Token: $webhookToken\n";
}

// إنشاء ملف .env
echo "\n📝 إنشاء ملف الإعدادات...\n";

$envContent = "# إعدادات التطبيق\n";
$envContent .= "APP_URL={$appUrl}\n";
$envContent .= "APP_DEBUG=false\n\n";

$envContent .= "# إعدادات قاعدة البيانات\n";
$envContent .= "DB_HOST={$dbConfig['host']}\n";
$envContent .= "DB_NAME={$dbConfig['name']}\n";
$envContent .= "DB_USERNAME={$dbConfig['username']}\n";
$envContent .= "DB_PASSWORD={$dbConfig['password']}\n\n";

$envContent .= "# إعدادات WhatsApp Business API\n";
$envContent .= "WHATSAPP_ACCESS_TOKEN={$whatsappToken}\n";
$envContent .= "WHATSAPP_PHONE_NUMBER_ID={$whatsappPhoneId}\n";
$envContent .= "WHATSAPP_WEBHOOK_VERIFY_TOKEN={$webhookToken}\n\n";

$envContent .= "# إعدادات الأمان\n";
$envContent .= "JWT_SECRET=" . bin2hex(random_bytes(32)) . "\n";
$envContent .= "ENCRYPTION_KEY=" . bin2hex(random_bytes(16)) . "\n\n";

$envContent .= "# إعدادات النظام\n";
$envContent .= "TIMEZONE={$timezone}\n";
$envContent .= "MAX_MESSAGES_PER_MINUTE=30\n";
$envContent .= "MAX_MESSAGES_PER_HOUR=100\n\n";

$envContent .= "# إعدادات السجلات\n";
$envContent .= "LOG_LEVEL=info\n\n";

$envContent .= "# إعدادات التخزين المؤقت\n";
$envContent .= "CACHE_ENABLED=true\n\n";

$envContent .= "# إعدادات الإشعارات\n";
$envContent .= "NOTIFICATIONS_ENABLED=true\n";
$envContent .= "EMAIL_NOTIFICATIONS=false\n";

if (file_put_contents('../.env', $envContent)) {
    echo "✅ تم إنشاء ملف الإعدادات (.env)\n";
} else {
    echo "❌ فشل في إنشاء ملف الإعدادات\n";
    exit(1);
}

// إنشاء حساب المدير
echo "\n👤 إنشاء حساب المدير:\n";

$adminUsername = readline("اسم المستخدم للمدير: ");
$adminEmail = readline("البريد الإلكتروني للمدير: ");
$adminFullname = readline("الاسم الكامل للمدير: ");

// إنشاء كلمة مرور قوية تلقائياً
$adminPassword = bin2hex(random_bytes(8));
echo "تم إنشاء كلمة مرور تلقائياً: $adminPassword\n";
echo "⚠️ احفظ هذه المعلومات في مكان آمن!\n";

if (empty($adminUsername) || empty($adminEmail) || empty($adminFullname)) {
    echo "❌ جميع بيانات المدير مطلوبة\n";
    exit(1);
}

try {
    $passwordHash = password_hash($adminPassword, PASSWORD_DEFAULT);
    
    $stmt = $pdo->prepare("
        INSERT INTO users (username, email, password_hash, full_name, role, is_active, created_at) 
        VALUES (?, ?, ?, ?, 'admin', 1, NOW())
    ");
    
    $stmt->execute([$adminUsername, $adminEmail, $passwordHash, $adminFullname]);
    echo "✅ تم إنشاء حساب المدير بنجاح\n";
    
} catch (PDOException $e) {
    echo "❌ فشل في إنشاء حساب المدير: " . $e->getMessage() . "\n";
    exit(1);
}

// إنشاء المجلدات المطلوبة
echo "\n📁 إنشاء المجلدات المطلوبة...\n";

$directories = ['../logs', '../uploads', '../reports', '../backups'];
foreach ($directories as $dir) {
    if (!is_dir($dir)) {
        if (mkdir($dir, 0777, true)) {
            echo "✅ تم إنشاء مجلد: " . basename($dir) . "\n";
        } else {
            echo "⚠️ فشل في إنشاء مجلد: " . basename($dir) . "\n";
        }
    } else {
        echo "ℹ️ مجلد موجود بالفعل: " . basename($dir) . "\n";
    }
}

// إنشاء ملفات الحماية
echo "\n🔒 إنشاء ملفات الحماية...\n";

$htaccessContent = "Deny from all\n";
$protectedDirs = ['../config', '../logs', '../backups'];

foreach ($protectedDirs as $dir) {
    if (is_dir($dir)) {
        if (file_put_contents($dir . '/.htaccess', $htaccessContent)) {
            echo "✅ تم حماية مجلد: " . basename($dir) . "\n";
        }
    }
}

// إنشاء ملف تأكيد التثبيت
file_put_contents('../install_complete.lock', date('Y-m-d H:i:s'));

// عرض ملخص التثبيت
echo "\n" . str_repeat('=', 60) . "\n";
echo "🎉 تم التثبيت بنجاح!\n";
echo str_repeat('=', 60) . "\n\n";

echo "📋 ملخص التثبيت:\n";
echo "  • رابط الموقع: $appUrl\n";
echo "  • اسم الشركة: $businessName\n";
echo "  • قاعدة البيانات: {$dbConfig['name']}\n";
echo "  • المدير: $adminUsername\n";
echo "  • البريد الإلكتروني: $adminEmail\n";
echo "  • كلمة المرور: $adminPassword\n\n";

echo "🔗 الروابط المهمة:\n";
echo "  • لوحة التحكم: $appUrl/admin/\n";
echo "  • Webhook URL: $appUrl/webhook.php\n";
echo "  • Webhook Token: $webhookToken\n\n";

echo "⚠️ خطوات مهمة بعد التثبيت:\n";
echo "  1. احذف مجلد install/ لأسباب أمنية\n";
echo "  2. أعد إعداد WhatsApp Business API إذا لم تدخل البيانات\n";
echo "  3. فعّل المهام المجدولة (Cron Jobs)\n";
echo "  4. أنشئ نسخة احتياطية من قاعدة البيانات\n";
echo "  5. فعّل SSL/HTTPS للموقع\n\n";

echo "📚 للمزيد من المعلومات، راجع ملف README.md\n";
echo "🆘 للدعم الفني، تواصل معنا على: <EMAIL>\n\n";

echo "شكراً لاستخدام نظام أتمتة WhatsApp Business! 🚀\n";
?>
