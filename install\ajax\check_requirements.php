<?php
/**
 * فحص المتطلبات عبر AJAX
 * AJAX Requirements Check
 */

header('Content-Type: application/json');

$requirements = [
    'php_version' => version_compare(PHP_VERSION, '8.0.0', '>='),
    'pdo_mysql' => extension_loaded('pdo_mysql'),
    'curl' => extension_loaded('curl'),
    'json' => extension_loaded('json'),
    'mbstring' => extension_loaded('mbstring'),
    'openssl' => extension_loaded('openssl'),
    'write_config' => is_writable('../../config/') || is_writable('../../'),
    'write_logs' => is_writable('../../logs/') || !file_exists('../../logs/'),
    'write_uploads' => is_writable('../../uploads/') || !file_exists('../../uploads/'),
    'write_reports' => is_writable('../../reports/') || !file_exists('../../reports/'),
    'write_backups' => is_writable('../../backups/') || !file_exists('../../backups/')
];

echo json_encode($requirements);
?>
