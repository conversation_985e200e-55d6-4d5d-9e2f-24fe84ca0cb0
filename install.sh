#!/bin/bash

# سكريبت التثبيت السريع لنظام أتمتة WhatsApp Business
# Quick Installation Script for WhatsApp Business Automation System

set -e

# الألوان للعرض
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# دالة طباعة الرسائل الملونة
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}$1${NC}"
}

# عرض الترحيب
clear
print_header "=================================================="
print_header "🚀 نظام أتمتة WhatsApp Business - التثبيت السريع"
print_header "=================================================="
echo ""

# التحقق من صلاحيات الجذر
if [[ $EUID -eq 0 ]]; then
   print_warning "يُنصح بعدم تشغيل هذا السكريبت كمستخدم جذر"
   read -p "هل تريد المتابعة؟ (y/N): " -n 1 -r
   echo
   if [[ ! $REPLY =~ ^[Yy]$ ]]; then
       exit 1
   fi
fi

# التحقق من نظام التشغيل
if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    OS="linux"
    print_status "تم اكتشاف نظام Linux"
elif [[ "$OSTYPE" == "darwin"* ]]; then
    OS="macos"
    print_status "تم اكتشاف نظام macOS"
else
    print_error "نظام التشغيل غير مدعوم: $OSTYPE"
    exit 1
fi

# التحقق من وجود PHP
if ! command -v php &> /dev/null; then
    print_error "PHP غير مثبت. يرجى تثبيت PHP 8.0 أو أحدث"
    exit 1
fi

# التحقق من إصدار PHP
PHP_VERSION=$(php -r "echo PHP_VERSION;")
if ! php -r "exit(version_compare(PHP_VERSION, '8.0.0', '>=') ? 0 : 1);"; then
    print_error "إصدار PHP غير مدعوم: $PHP_VERSION. مطلوب PHP 8.0 أو أحدث"
    exit 1
fi

print_status "إصدار PHP: $PHP_VERSION ✓"

# التحقق من وجود MySQL/MariaDB
if ! command -v mysql &> /dev/null; then
    print_error "MySQL/MariaDB غير مثبت"
    exit 1
fi

print_status "MySQL/MariaDB متوفر ✓"

# التحقق من إضافات PHP المطلوبة
print_status "فحص إضافات PHP المطلوبة..."

REQUIRED_EXTENSIONS=("pdo_mysql" "curl" "json" "mbstring" "openssl")
MISSING_EXTENSIONS=()

for ext in "${REQUIRED_EXTENSIONS[@]}"; do
    if ! php -m | grep -q "^$ext$"; then
        MISSING_EXTENSIONS+=("$ext")
    else
        print_status "  ✓ $ext"
    fi
done

if [ ${#MISSING_EXTENSIONS[@]} -ne 0 ]; then
    print_error "إضافات PHP مفقودة:"
    for ext in "${MISSING_EXTENSIONS[@]}"; do
        echo "  - $ext"
    done
    
    if [[ $OS == "linux" ]]; then
        print_status "لتثبيت الإضافات المفقودة على Ubuntu/Debian:"
        echo "sudo apt update"
        for ext in "${MISSING_EXTENSIONS[@]}"; do
            echo "sudo apt install php-$ext"
        done
    fi
    
    exit 1
fi

# إنشاء المجلدات المطلوبة
print_status "إنشاء المجلدات المطلوبة..."

DIRECTORIES=("logs" "uploads" "reports" "backups" "config")
for dir in "${DIRECTORIES[@]}"; do
    if [ ! -d "$dir" ]; then
        mkdir -p "$dir"
        print_status "  ✓ تم إنشاء مجلد: $dir"
    else
        print_status "  ✓ مجلد موجود: $dir"
    fi
done

# تعيين الصلاحيات
print_status "تعيين صلاحيات المجلدات..."
chmod 755 config/
chmod 777 logs/ uploads/ reports/ backups/

# التحقق من وجود Composer (اختياري)
if command -v composer &> /dev/null; then
    print_status "Composer متوفر ✓"
    
    # تثبيت التبعيات إذا كان ملف composer.json موجود
    if [ -f "composer.json" ]; then
        print_status "تثبيت تبعيات PHP..."
        composer install --no-dev --optimize-autoloader
    fi
else
    print_warning "Composer غير مثبت (اختياري)"
fi

# نسخ ملف الإعدادات
if [ ! -f ".env" ]; then
    if [ -f ".env.example" ]; then
        cp .env.example .env
        print_status "تم نسخ ملف الإعدادات من .env.example"
    else
        print_status "إنشاء ملف إعدادات أساسي..."
        cat > .env << EOF
# إعدادات التطبيق
APP_URL=http://localhost
APP_DEBUG=false

# إعدادات قاعدة البيانات
DB_HOST=localhost
DB_NAME=whatsapp_automation
DB_USERNAME=root
DB_PASSWORD=

# إعدادات WhatsApp Business API
WHATSAPP_ACCESS_TOKEN=your_access_token_here
WHATSAPP_PHONE_NUMBER_ID=your_phone_number_id_here
WHATSAPP_WEBHOOK_VERIFY_TOKEN=your_webhook_verify_token_here

# إعدادات الأمان
JWT_SECRET=$(openssl rand -hex 32)
ENCRYPTION_KEY=$(openssl rand -hex 16)

# إعدادات النظام
TIMEZONE=Asia/Riyadh
MAX_MESSAGES_PER_MINUTE=30
MAX_MESSAGES_PER_HOUR=100

# إعدادات السجلات
LOG_LEVEL=info

# إعدادات التخزين المؤقت
CACHE_ENABLED=true

# إعدادات الإشعارات
NOTIFICATIONS_ENABLED=true
EMAIL_NOTIFICATIONS=false
EOF
    fi
else
    print_warning "ملف .env موجود بالفعل"
fi

# إنشاء ملفات الحماية
print_status "إنشاء ملفات الحماية..."

# حماية مجلد config
cat > config/.htaccess << EOF
Deny from all
EOF

# حماية مجلد logs
cat > logs/.htaccess << EOF
Deny from all
EOF

# حماية مجلد backups
cat > backups/.htaccess << EOF
Deny from all
EOF

# إنشاء ملف index.php للحماية
for dir in logs uploads reports backups; do
    cat > $dir/index.php << EOF
<?php
// منع الوصول المباشر
header('HTTP/1.0 403 Forbidden');
exit('Access Denied');
?>
EOF
done

print_status "تم إنشاء ملفات الحماية ✓"

# التحقق من خادم الويب
print_status "فحص إعداد خادم الويب..."

if command -v apache2 &> /dev/null || command -v httpd &> /dev/null; then
    print_status "Apache متوفر ✓"
    
    # إنشاء ملف .htaccess أساسي
    if [ ! -f ".htaccess" ]; then
        cat > .htaccess << EOF
RewriteEngine On

# إعادة توجيه HTTP إلى HTTPS (اختياري)
# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# حماية الملفات الحساسة
<Files ".env">
    Order allow,deny
    Deny from all
</Files>

<Files "*.log">
    Order allow,deny
    Deny from all
</Files>

# تحسين الأداء
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
</IfModule>

<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>
EOF
        print_status "تم إنشاء ملف .htaccess"
    fi
    
elif command -v nginx &> /dev/null; then
    print_status "Nginx متوفر ✓"
    print_warning "تأكد من إعداد Nginx بشكل صحيح لـ PHP"
else
    print_warning "لم يتم اكتشاف خادم ويب. تأكد من تثبيت Apache أو Nginx"
fi

# عرض معلومات ما بعد التثبيت
print_header ""
print_header "=================================================="
print_header "✅ تم إكمال التثبيت الأساسي بنجاح!"
print_header "=================================================="
echo ""

print_status "الخطوات التالية:"
echo "1. قم بتحرير ملف .env وأدخل إعدادات قاعدة البيانات"
echo "2. قم بزيارة: http://yourdomain.com/install/ لإكمال التثبيت"
echo "3. أو استخدم التثبيت من سطر الأوامر: php install/cli_install.php"
echo ""

print_status "الملفات والمجلدات المُنشأة:"
echo "  ✓ .env - ملف الإعدادات"
echo "  ✓ .htaccess - إعدادات Apache"
echo "  ✓ logs/ - مجلد السجلات"
echo "  ✓ uploads/ - مجلد الرفع"
echo "  ✓ reports/ - مجلد التقارير"
echo "  ✓ backups/ - مجلد النسخ الاحتياطية"
echo ""

print_warning "ملاحظات مهمة:"
echo "  • تأكد من إعداد قاعدة البيانات MySQL"
echo "  • احصل على WhatsApp Business API credentials"
echo "  • فعّل SSL/HTTPS للإنتاج"
echo "  • أعد إعداد صلاحيات الملفات حسب الحاجة"
echo ""

print_status "للدعم الفني: https://github.com/your-repo/whatsapp-automation"
print_status "شكراً لاستخدام نظام أتمتة WhatsApp Business! 🚀"

# اختبار بسيط للتأكد من عمل PHP
if php -r "echo 'PHP يعمل بشكل صحيح';" &> /dev/null; then
    print_status "اختبار PHP: ✅ نجح"
else
    print_error "اختبار PHP: ❌ فشل"
fi

echo ""
print_header "انتهى التثبيت الأساسي. استخدم معالج التثبيت لإكمال الإعداد."
