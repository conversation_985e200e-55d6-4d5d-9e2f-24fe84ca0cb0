<?php
/**
 * الخطوة الرابعة: إنشاء حساب المدير
 * Step 4: Admin Account Creation
 */

$errors = [];
$success = [];

if ($_POST) {
    $admin_username = trim($_POST['admin_username'] ?? '');
    $admin_email = trim($_POST['admin_email'] ?? '');
    $admin_password = $_POST['admin_password'] ?? '';
    $admin_password_confirm = $_POST['admin_password_confirm'] ?? '';
    $admin_fullname = trim($_POST['admin_fullname'] ?? '');
    
    // التحقق من البيانات المطلوبة
    if (empty($admin_username)) {
        $errors[] = 'اسم المستخدم مطلوب';
    } elseif (strlen($admin_username) < 3) {
        $errors[] = 'اسم المستخدم يجب أن يكون 3 أحرف على الأقل';
    }
    
    if (empty($admin_email)) {
        $errors[] = 'البريد الإلكتروني مطلوب';
    } elseif (!filter_var($admin_email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'البريد الإلكتروني غير صحيح';
    }
    
    if (empty($admin_password)) {
        $errors[] = 'كلمة المرور مطلوبة';
    } elseif (strlen($admin_password) < 8) {
        $errors[] = 'كلمة المرور يجب أن تكون 8 أحرف على الأقل';
    }
    
    if ($admin_password !== $admin_password_confirm) {
        $errors[] = 'كلمات المرور غير متطابقة';
    }
    
    if (empty($admin_fullname)) {
        $errors[] = 'الاسم الكامل مطلوب';
    }
    
    if (empty($errors)) {
        try {
            $pdo = new PDO(
                "mysql:host={$_SESSION['db_config']['host']};dbname={$_SESSION['db_config']['name']};charset=utf8mb4",
                $_SESSION['db_config']['username'],
                $_SESSION['db_config']['password'],
                [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
            );
            
            // التحقق من عدم وجود مستخدم بنفس الاسم أو البريد
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE username = ? OR email = ?");
            $stmt->execute([$admin_username, $admin_email]);
            
            if ($stmt->fetchColumn() > 0) {
                $errors[] = 'اسم المستخدم أو البريد الإلكتروني موجود بالفعل';
            } else {
                // إنشاء حساب المدير
                $password_hash = password_hash($admin_password, PASSWORD_DEFAULT);
                
                $stmt = $pdo->prepare("
                    INSERT INTO users (username, email, password_hash, full_name, role, is_active, created_at) 
                    VALUES (?, ?, ?, ?, 'admin', 1, NOW())
                ");
                
                if ($stmt->execute([$admin_username, $admin_email, $password_hash, $admin_fullname])) {
                    $success[] = 'تم إنشاء حساب المدير بنجاح';
                    
                    // حذف المستخدم الافتراضي إذا كان موجوداً
                    $pdo->exec("DELETE FROM users WHERE username = 'admin' AND id != LAST_INSERT_ID()");
                    
                    // حفظ بيانات المدير في الجلسة
                    $_SESSION['admin_created'] = true;
                    $_SESSION['admin_username'] = $admin_username;
                    
                    // إنشاء المجلدات المطلوبة
                    $directories = ['../logs', '../uploads', '../reports', '../backups'];
                    foreach ($directories as $dir) {
                        if (!is_dir($dir)) {
                            mkdir($dir, 0777, true);
                        }
                    }
                    
                    // إنشاء ملف .htaccess لحماية المجلدات الحساسة
                    $htaccessContent = "Deny from all\n";
                    $protectedDirs = ['../config', '../logs', '../backups'];
                    foreach ($protectedDirs as $dir) {
                        if (is_dir($dir)) {
                            file_put_contents($dir . '/.htaccess', $htaccessContent);
                        }
                    }
                    
                    $success[] = 'تم إنشاء المجلدات المطلوبة وحمايتها';
                    
                    // إعادة توجيه للخطوة الأخيرة
                    header('Location: ?step=5');
                    exit;
                } else {
                    $errors[] = 'فشل في إنشاء حساب المدير';
                }
            }
        } catch (PDOException $e) {
            $errors[] = 'خطأ في قاعدة البيانات: ' . $e->getMessage();
        }
    }
}
?>

<h3 class="mb-4">
    <i class="fas fa-user-shield text-primary"></i>
    إنشاء حساب المدير
</h3>

<p class="text-muted mb-4">
    أنشئ حساب المدير الرئيسي للنظام. هذا الحساب سيكون له صلاحيات كاملة لإدارة النظام.
</p>

<?php if (!empty($errors)): ?>
    <div class="alert alert-danger">
        <i class="fas fa-exclamation-triangle"></i>
        <strong>خطأ!</strong>
        <ul class="mb-0 mt-2">
            <?php foreach ($errors as $error): ?>
                <li><?php echo htmlspecialchars($error); ?></li>
            <?php endforeach; ?>
        </ul>
    </div>
<?php endif; ?>

<?php if (!empty($success)): ?>
    <div class="alert alert-success">
        <i class="fas fa-check-circle"></i>
        <strong>نجح!</strong>
        <ul class="mb-0 mt-2">
            <?php foreach ($success as $msg): ?>
                <li><?php echo htmlspecialchars($msg); ?></li>
            <?php endforeach; ?>
        </ul>
    </div>
<?php endif; ?>

<form method="POST" id="adminForm">
    <div class="row">
        <div class="col-md-6 mb-3">
            <label for="admin_username" class="form-label">
                <i class="fas fa-user"></i> اسم المستخدم *
            </label>
            <input type="text" class="form-control" id="admin_username" name="admin_username" 
                   value="<?php echo htmlspecialchars($_POST['admin_username'] ?? ''); ?>" 
                   required minlength="3" maxlength="50">
            <small class="form-text text-muted">3-50 حرف، أحرف وأرقام فقط</small>
        </div>
        
        <div class="col-md-6 mb-3">
            <label for="admin_email" class="form-label">
                <i class="fas fa-envelope"></i> البريد الإلكتروني *
            </label>
            <input type="email" class="form-control" id="admin_email" name="admin_email" 
                   value="<?php echo htmlspecialchars($_POST['admin_email'] ?? ''); ?>" required>
        </div>
    </div>
    
    <div class="mb-3">
        <label for="admin_fullname" class="form-label">
            <i class="fas fa-id-card"></i> الاسم الكامل *
        </label>
        <input type="text" class="form-control" id="admin_fullname" name="admin_fullname" 
               value="<?php echo htmlspecialchars($_POST['admin_fullname'] ?? ''); ?>" required>
    </div>
    
    <div class="row">
        <div class="col-md-6 mb-3">
            <label for="admin_password" class="form-label">
                <i class="fas fa-lock"></i> كلمة المرور *
            </label>
            <input type="password" class="form-control" id="admin_password" name="admin_password" 
                   required minlength="8">
            <small class="form-text text-muted">8 أحرف على الأقل</small>
            <div class="password-strength mt-2">
                <div class="progress" style="height: 5px;">
                    <div class="progress-bar" id="passwordStrength" role="progressbar" style="width: 0%"></div>
                </div>
                <small id="passwordStrengthText" class="text-muted"></small>
            </div>
        </div>
        
        <div class="col-md-6 mb-3">
            <label for="admin_password_confirm" class="form-label">
                <i class="fas fa-lock"></i> تأكيد كلمة المرور *
            </label>
            <input type="password" class="form-control" id="admin_password_confirm" name="admin_password_confirm" 
                   required minlength="8">
            <small id="passwordMatch" class="form-text"></small>
        </div>
    </div>
    
    <div class="alert alert-info">
        <i class="fas fa-info-circle"></i>
        <strong>ملاحظات مهمة:</strong>
        <ul class="mb-0 mt-2">
            <li>احفظ بيانات تسجيل الدخول في مكان آمن</li>
            <li>يمكنك تغيير كلمة المرور لاحقاً من لوحة التحكم</li>
            <li>يمكنك إضافة مستخدمين آخرين بعد التثبيت</li>
            <li>سيتم حذف المستخدم الافتراضي (admin) إذا كان موجوداً</li>
        </ul>
    </div>
    
    <div class="d-flex justify-content-between">
        <a href="?step=3" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i> السابق
        </a>
        
        <button type="submit" class="btn btn-success btn-install" id="submitBtn">
            <i class="fas fa-user-plus"></i> إنشاء الحساب والمتابعة
        </button>
    </div>
</form>

<div class="progress mt-4">
    <div class="progress-bar" role="progressbar" style="width: 80%" aria-valuenow="80" aria-valuemin="0" aria-valuemax="100"></div>
</div>

<script>
// فحص قوة كلمة المرور
function checkPasswordStrength(password) {
    let strength = 0;
    let text = '';
    let color = '';
    
    if (password.length >= 8) strength += 25;
    if (password.match(/[a-z]/)) strength += 25;
    if (password.match(/[A-Z]/)) strength += 25;
    if (password.match(/[0-9]/)) strength += 25;
    if (password.match(/[^a-zA-Z0-9]/)) strength += 25;
    
    if (strength <= 25) {
        text = 'ضعيفة جداً';
        color = 'bg-danger';
    } else if (strength <= 50) {
        text = 'ضعيفة';
        color = 'bg-warning';
    } else if (strength <= 75) {
        text = 'متوسطة';
        color = 'bg-info';
    } else {
        text = 'قوية';
        color = 'bg-success';
    }
    
    return { strength: Math.min(strength, 100), text, color };
}

// التحقق من تطابق كلمات المرور
function checkPasswordMatch() {
    const password = document.getElementById('admin_password').value;
    const confirm = document.getElementById('admin_password_confirm').value;
    const matchElement = document.getElementById('passwordMatch');
    
    if (confirm === '') {
        matchElement.textContent = '';
        matchElement.className = 'form-text';
        return true;
    }
    
    if (password === confirm) {
        matchElement.textContent = '✓ كلمات المرور متطابقة';
        matchElement.className = 'form-text text-success';
        return true;
    } else {
        matchElement.textContent = '✗ كلمات المرور غير متطابقة';
        matchElement.className = 'form-text text-danger';
        return false;
    }
}

// إعداد مستمعي الأحداث
document.addEventListener('DOMContentLoaded', function() {
    const passwordInput = document.getElementById('admin_password');
    const confirmInput = document.getElementById('admin_password_confirm');
    const strengthBar = document.getElementById('passwordStrength');
    const strengthText = document.getElementById('passwordStrengthText');
    const submitBtn = document.getElementById('submitBtn');
    
    passwordInput.addEventListener('input', function() {
        const result = checkPasswordStrength(this.value);
        strengthBar.style.width = result.strength + '%';
        strengthBar.className = 'progress-bar ' + result.color;
        strengthText.textContent = result.text;
        
        checkPasswordMatch();
        updateSubmitButton();
    });
    
    confirmInput.addEventListener('input', function() {
        checkPasswordMatch();
        updateSubmitButton();
    });
    
    function updateSubmitButton() {
        const password = passwordInput.value;
        const confirm = confirmInput.value;
        const isValid = password.length >= 8 && password === confirm;
        
        submitBtn.disabled = !isValid;
        submitBtn.className = isValid ? 'btn btn-success btn-install' : 'btn btn-secondary btn-install';
    }
    
    // التحقق من صحة النموذج قبل الإرسال
    document.getElementById('adminForm').addEventListener('submit', function(e) {
        if (!checkPasswordMatch()) {
            e.preventDefault();
            alert('يرجى التأكد من تطابق كلمات المرور');
        }
    });
});

// إنشاء كلمة مرور قوية
function generateStrongPassword() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
    let password = '';
    for (let i = 0; i < 12; i++) {
        password += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    
    document.getElementById('admin_password').value = password;
    document.getElementById('admin_password_confirm').value = password;
    
    // تحديث مؤشر القوة
    const result = checkPasswordStrength(password);
    document.getElementById('passwordStrength').style.width = result.strength + '%';
    document.getElementById('passwordStrength').className = 'progress-bar ' + result.color;
    document.getElementById('passwordStrengthText').textContent = result.text;
    
    checkPasswordMatch();
}

// إضافة زر إنشاء كلمة مرور
const generateBtn = document.createElement('button');
generateBtn.type = 'button';
generateBtn.className = 'btn btn-outline-secondary btn-sm mt-1';
generateBtn.innerHTML = '<i class="fas fa-key"></i> إنشاء كلمة مرور قوية';
generateBtn.onclick = generateStrongPassword;

document.getElementById('admin_password').parentNode.appendChild(generateBtn);
</script>
