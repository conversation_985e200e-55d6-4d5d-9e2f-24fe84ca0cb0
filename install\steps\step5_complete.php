<?php
/**
 * الخطوة الخامسة: اكتمال التثبيت
 * Step 5: Installation Complete
 */

// إنشاء ملف تأكيد التثبيت
$installationComplete = false;
if (!file_exists('../install_complete.lock')) {
    if (file_put_contents('../install_complete.lock', date('Y-m-d H:i:s'))) {
        $installationComplete = true;
    }
}

// معلومات التثبيت
$installInfo = [
    'تاريخ التثبيت' => date('Y-m-d H:i:s'),
    'إصدار PHP' => PHP_VERSION,
    'قاعدة البيانات' => $_SESSION['db_config']['name'] ?? 'غير محدد',
    'اسم الشركة' => $_SESSION['config']['business_name'] ?? 'غير محدد',
    'المدير' => $_SESSION['admin_username'] ?? 'غير محدد'
];
?>

<div class="text-center mb-4">
    <i class="fas fa-check-circle text-success" style="font-size: 4rem;"></i>
    <h2 class="text-success mt-3">تم التثبيت بنجاح!</h2>
    <p class="text-muted">نظام أتمتة WhatsApp Business جاهز للاستخدام</p>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0"><i class="fas fa-info-circle"></i> معلومات التثبيت</h5>
            </div>
            <div class="card-body">
                <?php foreach ($installInfo as $key => $value): ?>
                    <div class="d-flex justify-content-between border-bottom py-2">
                        <strong><?php echo $key; ?>:</strong>
                        <span><?php echo htmlspecialchars($value); ?></span>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-rocket"></i> الخطوات التالية</h5>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    <div class="list-group-item d-flex align-items-center">
                        <i class="fas fa-sign-in-alt text-primary me-3"></i>
                        <div>
                            <strong>تسجيل الدخول</strong>
                            <small class="d-block text-muted">ادخل إلى لوحة التحكم</small>
                        </div>
                    </div>
                    
                    <div class="list-group-item d-flex align-items-center">
                        <i class="fab fa-whatsapp text-success me-3"></i>
                        <div>
                            <strong>إعداد WhatsApp API</strong>
                            <small class="d-block text-muted">أدخل بيانات WhatsApp Business</small>
                        </div>
                    </div>
                    
                    <div class="list-group-item d-flex align-items-center">
                        <i class="fas fa-cog text-info me-3"></i>
                        <div>
                            <strong>تخصيص الإعدادات</strong>
                            <small class="d-block text-muted">اضبط إعدادات النظام</small>
                        </div>
                    </div>
                    
                    <div class="list-group-item d-flex align-items-center">
                        <i class="fas fa-robot text-warning me-3"></i>
                        <div>
                            <strong>إعداد الردود التلقائية</strong>
                            <small class="d-block text-muted">أنشئ ردود ذكية</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="alert alert-info mt-4">
    <i class="fas fa-lightbulb"></i>
    <strong>نصائح مهمة:</strong>
    <ul class="mb-0 mt-2">
        <li><strong>احذف مجلد التثبيت:</strong> لأسباب أمنية، احذف مجلد <code>install/</code> بعد اكتمال التثبيت</li>
        <li><strong>إعداد Webhook:</strong> استخدم الرابط <code><?php echo $_SESSION['config']['app_url'] ?? ''; ?>/webhook.php</code> في إعدادات WhatsApp</li>
        <li><strong>إعداد Cron Jobs:</strong> أضف المهام المجدولة لتشغيل الميزات التلقائية</li>
        <li><strong>النسخ الاحتياطي:</strong> فعّل النسخ الاحتياطي التلقائي من الإعدادات</li>
    </ul>
</div>

<div class="card mt-4">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-terminal"></i> إعداد Cron Jobs (اختياري)</h5>
    </div>
    <div class="card-body">
        <p>لتفعيل الميزات التلقائية، أضف هذه المهام المجدولة:</p>
        <pre class="bg-dark text-light p-3 rounded"><code># معالجة الرسائل المجدولة كل 5 دقائق
*/5 * * * * /usr/bin/php <?php echo realpath('../cron/scheduler.php'); ?>

# تنظيف البيانات يومياً في الساعة 2:00 صباحاً
0 2 * * * /usr/bin/php <?php echo realpath('../cron/cleanup.php'); ?>

# نسخة احتياطية يومياً في الساعة 3:00 صباحاً
0 3 * * * <?php echo realpath('../scripts/backup.sh'); ?></code></pre>
        
        <div class="alert alert-warning mt-3">
            <i class="fas fa-exclamation-triangle"></i>
            <strong>ملاحظة:</strong> تأكد من تعديل مسارات الملفات حسب موقع التثبيت الفعلي
        </div>
    </div>
</div>

<div class="card mt-4">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-shield-alt"></i> إرشادات الأمان</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <h6><i class="fas fa-lock text-danger"></i> مطلوب فوراً:</h6>
                <ul>
                    <li>احذف مجلد <code>install/</code></li>
                    <li>غيّر كلمات المرور الافتراضية</li>
                    <li>فعّل SSL/HTTPS</li>
                    <li>قم بتحديث النظام بانتظام</li>
                </ul>
            </div>
            <div class="col-md-6">
                <h6><i class="fas fa-user-shield text-warning"></i> مُوصى به:</h6>
                <ul>
                    <li>فعّل المصادقة الثنائية</li>
                    <li>راقب سجلات النظام</li>
                    <li>أنشئ نسخ احتياطية منتظمة</li>
                    <li>قيّد الوصول حسب IP</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<div class="text-center mt-4">
    <a href="../admin/" class="btn btn-success btn-lg">
        <i class="fas fa-tachometer-alt"></i> دخول لوحة التحكم
    </a>
    
    <a href="../" class="btn btn-outline-primary btn-lg ms-2">
        <i class="fas fa-home"></i> الصفحة الرئيسية
    </a>
</div>

<div class="progress mt-4">
    <div class="progress-bar bg-success" role="progressbar" style="width: 100%" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100"></div>
</div>

<div class="text-center mt-4">
    <small class="text-muted">
        نظام أتمتة WhatsApp Business - الإصدار 1.0.0<br>
        تم التطوير بواسطة فريق التطوير
    </small>
</div>

<script>
// إظهار رسالة تأكيد عند محاولة مغادرة الصفحة
window.addEventListener('beforeunload', function(e) {
    const message = 'تأكد من حفظ معلومات تسجيل الدخول قبل المغادرة';
    e.returnValue = message;
    return message;
});

// إزالة التحذير عند النقر على أزرار الانتقال
document.querySelectorAll('a[href]').forEach(function(link) {
    link.addEventListener('click', function() {
        window.removeEventListener('beforeunload', arguments.callee);
    });
});

// عرض معلومات إضافية
function showInstallationDetails() {
    const details = `
تفاصيل التثبيت:
================
التاريخ: <?php echo date('Y-m-d H:i:s'); ?>
الخادم: <?php echo $_SERVER['SERVER_NAME'] ?? 'غير محدد'; ?>
PHP: <?php echo PHP_VERSION; ?>
قاعدة البيانات: <?php echo $_SESSION['db_config']['name'] ?? 'غير محدد'; ?>
المدير: <?php echo $_SESSION['admin_username'] ?? 'غير محدد'; ?>

الملفات المُنشأة:
- .env (إعدادات النظام)
- install_complete.lock (تأكيد التثبيت)
- المجلدات: logs, uploads, reports, backups

الخطوات التالية:
1. احذف مجلد install/
2. ادخل لوحة التحكم
3. أعد إعداد WhatsApp API
4. أنشئ ردود تلقائية
5. فعّل المهام المجدولة
    `;
    
    alert(details);
}

// إضافة زر عرض التفاصيل
const detailsBtn = document.createElement('button');
detailsBtn.type = 'button';
detailsBtn.className = 'btn btn-outline-info btn-sm mt-2';
detailsBtn.innerHTML = '<i class="fas fa-info"></i> عرض تفاصيل التثبيت';
detailsBtn.onclick = showInstallationDetails;

document.querySelector('.text-center:last-child').appendChild(detailsBtn);

// تحديث الوقت كل ثانية
function updateTime() {
    const now = new Date();
    const timeString = now.toLocaleString('ar-SA');
    document.title = 'تم التثبيت بنجاح - ' + timeString;
}

setInterval(updateTime, 1000);
</script>

<?php
// تنظيف بيانات الجلسة
unset($_SESSION['db_config']);
unset($_SESSION['config']);
unset($_SESSION['admin_created']);
?>

<style>
.card {
    transition: transform 0.2s;
}

.card:hover {
    transform: translateY(-2px);
}

.list-group-item {
    border: none;
    padding: 15px 0;
}

.list-group-item:not(:last-child) {
    border-bottom: 1px solid #f0f0f0;
}

pre code {
    font-size: 0.9em;
    line-height: 1.4;
}

.alert ul {
    padding-right: 20px;
}

.btn-lg {
    padding: 12px 30px;
    font-size: 1.1em;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.card, .alert {
    animation: fadeIn 0.6s ease-out;
}
</style>
