<?php
/**
 * تسجيل الخروج
 * Logout Page
 */

session_start();
require_once '../config/config.php';

// تسجيل نشاط تسجيل الخروج
if (isset($_SESSION['user_id'])) {
    try {
        $database->insert('activity_logs', [
            'user_id' => $_SESSION['user_id'],
            'action' => 'logout',
            'details' => json_encode(['ip' => $_SERVER['REMOTE_ADDR']]),
            'ip_address' => $_SERVER['REMOTE_ADDR'],
            'user_agent' => $_SERVER['HTTP_USER_AGENT'],
            'created_at' => date('Y-m-d H:i:s')
        ]);
    } catch (Exception $e) {
        // تجاهل الأخطاء في تسجيل النشاط
    }
}

// مسح جميع بيانات الجلسة
session_unset();
session_destroy();

// حذف ملف تعريف الارتباط للجلسة
if (ini_get("session.use_cookies")) {
    $params = session_get_cookie_params();
    setcookie(session_name(), '', time() - 42000,
        $params["path"], $params["domain"],
        $params["secure"], $params["httponly"]
    );
}

// إعادة التوجيه إلى صفحة تسجيل الدخول
header('Location: login.php?message=logged_out');
exit;
?>
