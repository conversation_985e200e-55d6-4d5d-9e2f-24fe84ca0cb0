<?php
/**
 * سكريبت إعادة تعيين كلمة مرور المدير
 * Admin Password Reset Script
 */

require_once 'config/config.php';

echo "🔐 سكريبت إعادة تعيين كلمة مرور المدير\n";
echo str_repeat('=', 50) . "\n\n";

try {
    // الاتصال بقاعدة البيانات
    $database = new Database();
    
    // التحقق من وجود المستخدم admin
    $admin = $database->fetchOne("SELECT * FROM users WHERE username = 'admin'");
    
    if (!$admin) {
        echo "❌ المستخدم 'admin' غير موجود\n";
        echo "إنشاء مستخدم admin جديد...\n";
        
        // إنشاء مستخدم admin جديد
        $passwordHash = password_hash('admin123', PASSWORD_DEFAULT);
        
        $adminId = $database->insert('users', [
            'username' => 'admin',
            'email' => '<EMAIL>',
            'password_hash' => $passwordHash,
            'full_name' => 'المدير العام',
            'role' => 'admin',
            'is_active' => 1
        ]);
        
        if ($adminId) {
            echo "✅ تم إنشاء المستخدم admin بنجاح\n";
            echo "📧 البريد الإلكتروني: <EMAIL>\n";
            echo "🔑 كلمة المرور: admin123\n";
        } else {
            echo "❌ فشل في إنشاء المستخدم\n";
        }
    } else {
        echo "👤 المستخدم موجود: {$admin['username']}\n";
        echo "📧 البريد الإلكتروني: {$admin['email']}\n";
        echo "إعادة تعيين كلمة المرور...\n";
        
        // إعادة تعيين كلمة المرور
        $passwordHash = password_hash('admin123', PASSWORD_DEFAULT);
        
        $updated = $database->update(
            'users',
            ['password_hash' => $passwordHash, 'updated_at' => date('Y-m-d H:i:s')],
            'username = ?',
            ['admin']
        );
        
        if ($updated) {
            echo "✅ تم إعادة تعيين كلمة المرور بنجاح\n";
            echo "🔑 كلمة المرور الجديدة: admin123\n";
        } else {
            echo "❌ فشل في إعادة تعيين كلمة المرور\n";
        }
    }
    
    echo "\n" . str_repeat('=', 50) . "\n";
    echo "🎯 بيانات تسجيل الدخول:\n";
    echo "   اسم المستخدم: admin\n";
    echo "   كلمة المرور: admin123\n";
    echo "   الرابط: " . (APP_URL ?? 'http://localhost') . "/admin/\n";
    echo "\n⚠️  تذكر تغيير كلمة المرور بعد تسجيل الدخول!\n";
    
    // اختبار كلمة المرور
    echo "\n🧪 اختبار كلمة المرور...\n";
    $testUser = $database->fetchOne("SELECT * FROM users WHERE username = 'admin'");
    
    if ($testUser && password_verify('admin123', $testUser['password_hash'])) {
        echo "✅ اختبار كلمة المرور نجح - يمكنك تسجيل الدخول الآن\n";
    } else {
        echo "❌ اختبار كلمة المرور فشل - هناك مشكلة\n";
    }
    
} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage() . "\n";
    echo "\n💡 تأكد من:\n";
    echo "   - إعدادات قاعدة البيانات في ملف .env\n";
    echo "   - أن قاعدة البيانات تعمل\n";
    echo "   - أن جدول users موجود\n";
}

echo "\n";
?>
