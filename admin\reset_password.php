<?php
/**
 * صفحة إعادة تعيين كلمة المرور (للطوارئ)
 * Emergency Password Reset Page
 */

// التحقق من وجود معامل الأمان
$securityKey = $_GET['key'] ?? '';
$expectedKey = 'reset_admin_2024'; // مفتاح أمان بسيط

if ($securityKey !== $expectedKey) {
    die('🔒 غير مصرح بالوصول. استخدم: ?key=reset_admin_2024');
}

require_once '../config/config.php';

$message = '';
$success = false;

if ($_POST) {
    try {
        $database = new Database();
        
        // إعادة تعيين كلمة المرور للمدير
        $passwordHash = password_hash('admin123', PASSWORD_DEFAULT);
        
        $updated = $database->update(
            'users',
            ['password_hash' => $passwordHash, 'updated_at' => date('Y-m-d H:i:s')],
            'username = ?',
            ['admin']
        );
        
        if ($updated) {
            $message = 'تم إعادة تعيين كلمة المرور بنجاح!';
            $success = true;
        } else {
            $message = 'فشل في إعادة تعيين كلمة المرور';
        }
        
    } catch (Exception $e) {
        $message = 'خطأ: ' . $e->getMessage();
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعادة تعيين كلمة المرور - نظام أتمتة WhatsApp Business</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .reset-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            padding: 40px;
            max-width: 500px;
            width: 100%;
        }
        .reset-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .reset-icon {
            font-size: 4rem;
            color: #dc3545;
            margin-bottom: 20px;
        }
        .btn-reset {
            background: #dc3545;
            border: none;
            padding: 12px 30px;
            border-radius: 8px;
            font-weight: bold;
            width: 100%;
        }
        .btn-reset:hover {
            background: #c82333;
        }
        .alert-custom {
            border-radius: 10px;
            border: none;
        }
        .login-info {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="reset-container">
        <div class="reset-header">
            <i class="fas fa-key reset-icon"></i>
            <h2>إعادة تعيين كلمة المرور</h2>
            <p class="text-muted">صفحة طوارئ لإعادة تعيين كلمة مرور المدير</p>
        </div>
        
        <?php if ($message): ?>
            <div class="alert alert-<?php echo $success ? 'success' : 'danger'; ?> alert-custom">
                <i class="fas fa-<?php echo $success ? 'check-circle' : 'exclamation-triangle'; ?>"></i>
                <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>
        
        <?php if (!$success): ?>
            <form method="POST">
                <div class="alert alert-warning alert-custom">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>تحذير!</strong> هذا الإجراء سيعيد تعيين كلمة مرور المدير إلى "admin123"
                </div>
                
                <div class="mb-3">
                    <label class="form-label">
                        <i class="fas fa-user"></i> اسم المستخدم
                    </label>
                    <input type="text" class="form-control" value="admin" readonly>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">
                        <i class="fas fa-lock"></i> كلمة المرور الجديدة
                    </label>
                    <input type="text" class="form-control" value="admin123" readonly>
                </div>
                
                <button type="submit" class="btn btn-danger btn-reset">
                    <i class="fas fa-sync-alt"></i> إعادة تعيين كلمة المرور
                </button>
            </form>
        <?php else: ?>
            <div class="login-info">
                <h5><i class="fas fa-info-circle text-success"></i> بيانات تسجيل الدخول</h5>
                <hr>
                <div class="row">
                    <div class="col-sm-4"><strong>اسم المستخدم:</strong></div>
                    <div class="col-sm-8"><code>admin</code></div>
                </div>
                <div class="row">
                    <div class="col-sm-4"><strong>كلمة المرور:</strong></div>
                    <div class="col-sm-8"><code>admin123</code></div>
                </div>
                <div class="row">
                    <div class="col-sm-4"><strong>رابط تسجيل الدخول:</strong></div>
                    <div class="col-sm-8"><a href="login.php" target="_blank">login.php</a></div>
                </div>
            </div>
            
            <div class="alert alert-info alert-custom mt-3">
                <i class="fas fa-shield-alt"></i>
                <strong>مهم:</strong> تذكر تغيير كلمة المرور بعد تسجيل الدخول لأسباب أمنية!
            </div>
            
            <div class="text-center mt-3">
                <a href="login.php" class="btn btn-success">
                    <i class="fas fa-sign-in-alt"></i> تسجيل الدخول الآن
                </a>
            </div>
        <?php endif; ?>
        
        <div class="text-center mt-4">
            <small class="text-muted">
                <i class="fas fa-exclamation-triangle"></i>
                احذف هذا الملف بعد حل المشكلة لأسباب أمنية
            </small>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تحذير أمني
        console.warn('🔒 تحذير أمني: هذه صفحة طوارئ لإعادة تعيين كلمة المرور');
        console.warn('🗑️ احذف هذا الملف بعد حل المشكلة');
        
        // إخفاء الصفحة بعد 10 دقائق لأسباب أمنية
        setTimeout(function() {
            document.body.innerHTML = '<div class="text-center mt-5"><h3>انتهت صلاحية الصفحة</h3><p>لأسباب أمنية، تم إغلاق هذه الصفحة تلقائياً</p></div>';
        }, 600000); // 10 دقائق
    </script>
</body>
</html>
