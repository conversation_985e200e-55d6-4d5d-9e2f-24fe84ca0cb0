<?php
/**
 * الصفحة الرئيسية لنظام أتمتة WhatsApp Business
 * Main Landing Page for WhatsApp Business Automation System
 */

// التحقق من وجود ملف التثبيت
if (file_exists('install/index.php') && !file_exists('install_complete.lock')) {
    header('Location: install/');
    exit;
}

// التحقق من وجود ملف الإعدادات
if (!file_exists('.env') || !file_exists('config/config.php')) {
    header('Location: install/');
    exit;
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام أتمتة WhatsApp Business - الصفحة الرئيسية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #25D366 0%, #128C7E 100%);
            min-height: 100vh;
        }
        
        .hero-section {
            padding: 100px 0;
            color: white;
            text-align: center;
        }
        
        .hero-title {
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .hero-subtitle {
            font-size: 1.3rem;
            margin-bottom: 40px;
            opacity: 0.9;
        }
        
        .feature-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            height: 100%;
        }
        
        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }
        
        .feature-icon {
            font-size: 3rem;
            color: #25D366;
            margin-bottom: 20px;
        }
        
        .feature-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 15px;
            color: #333;
        }
        
        .feature-description {
            color: #666;
            line-height: 1.6;
        }
        
        .stats-section {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            margin: 50px 0;
        }
        
        .stat-item {
            text-align: center;
            color: white;
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            display: block;
        }
        
        .stat-label {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .cta-section {
            background: white;
            padding: 80px 0;
            text-align: center;
        }
        
        .btn-primary-custom {
            background: #25D366;
            border: none;
            padding: 15px 40px;
            font-size: 1.2rem;
            font-weight: 600;
            border-radius: 50px;
            transition: all 0.3s ease;
        }
        
        .btn-primary-custom:hover {
            background: #128C7E;
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(37, 211, 102, 0.3);
        }
        
        .btn-outline-custom {
            border: 2px solid #25D366;
            color: #25D366;
            padding: 15px 40px;
            font-size: 1.2rem;
            font-weight: 600;
            border-radius: 50px;
            background: transparent;
            transition: all 0.3s ease;
        }
        
        .btn-outline-custom:hover {
            background: #25D366;
            color: white;
            transform: translateY(-2px);
        }
        
        .footer {
            background: #1a1a1a;
            color: white;
            padding: 40px 0;
            text-align: center;
        }
        
        .whatsapp-float {
            position: fixed;
            bottom: 30px;
            left: 30px;
            background: #25D366;
            color: white;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            box-shadow: 0 5px 15px rgba(37, 211, 102, 0.4);
            z-index: 1000;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
        
        .navbar-custom {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border: none;
        }
        
        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
        }
        
        .nav-link {
            font-weight: 500;
            margin: 0 10px;
        }
        
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
            }
            
            .hero-subtitle {
                font-size: 1.1rem;
            }
            
            .feature-card {
                margin-bottom: 20px;
            }
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark navbar-custom fixed-top">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fab fa-whatsapp me-2"></i>
                نظام أتمتة WhatsApp Business
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="#features">المميزات</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#about">حول النظام</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="admin/" target="_blank">
                            <i class="fas fa-tachometer-alt me-1"></i>
                            لوحة التحكم
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- القسم الرئيسي -->
    <section class="hero-section">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mx-auto">
                    <h1 class="hero-title">
                        <i class="fab fa-whatsapp me-3"></i>
                        نظام أتمتة WhatsApp Business
                    </h1>
                    <p class="hero-subtitle">
                        حل شامل ومتطور لأتمتة WhatsApp Business مع ميزات الذكاء الاصطناعي والتقارير المتقدمة
                    </p>
                    
                    <div class="d-flex justify-content-center gap-3 flex-wrap">
                        <a href="admin/" class="btn btn-primary-custom">
                            <i class="fas fa-rocket me-2"></i>
                            ابدأ الآن
                        </a>
                        <a href="#features" class="btn btn-outline-custom">
                            <i class="fas fa-info-circle me-2"></i>
                            اكتشف المميزات
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- إحصائيات سريعة -->
            <div class="stats-section">
                <div class="row">
                    <div class="col-md-3 col-6">
                        <div class="stat-item">
                            <span class="stat-number">24/7</span>
                            <span class="stat-label">خدمة مستمرة</span>
                        </div>
                    </div>
                    <div class="col-md-3 col-6">
                        <div class="stat-item">
                            <span class="stat-number">100%</span>
                            <span class="stat-label">أتمتة ذكية</span>
                        </div>
                    </div>
                    <div class="col-md-3 col-6">
                        <div class="stat-item">
                            <span class="stat-number">∞</span>
                            <span class="stat-label">رسائل غير محدودة</span>
                        </div>
                    </div>
                    <div class="col-md-3 col-6">
                        <div class="stat-item">
                            <span class="stat-number">⚡</span>
                            <span class="stat-label">سرعة فائقة</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- قسم المميزات -->
    <section id="features" class="cta-section">
        <div class="container">
            <div class="row mb-5">
                <div class="col-lg-8 mx-auto text-center">
                    <h2 class="display-4 fw-bold text-dark mb-4">مميزات النظام</h2>
                    <p class="lead text-muted">
                        نظام شامل يوفر جميع الأدوات اللازمة لإدارة وأتمتة WhatsApp Business بكفاءة عالية
                    </p>
                </div>
            </div>
            
            <div class="row">
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-robot"></i>
                        </div>
                        <h3 class="feature-title">ردود تلقائية ذكية</h3>
                        <p class="feature-description">
                            نظام ردود تلقائية متطور يفهم السياق ويرد بذكاء على استفسارات العملاء
                        </p>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-mouse-pointer"></i>
                        </div>
                        <h3 class="feature-title">أزرار تفاعلية</h3>
                        <p class="feature-description">
                            أزرار قابلة للنقر داخل المحادثات مع قوائم منسدلة لتحسين تجربة المستخدم
                        </p>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-chart-bar"></i>
                        </div>
                        <h3 class="feature-title">تقارير شاملة</h3>
                        <p class="feature-description">
                            تقارير مفصلة وإحصائيات متقدمة مع رسوم بيانية تفاعلية لمتابعة الأداء
                        </p>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <h3 class="feature-title">إدارة العملاء</h3>
                        <p class="feature-description">
                            نظام شامل لإدارة جهات الاتصال مع تصنيف ذكي وتتبع تفاعل كل عميل
                        </p>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-bullhorn"></i>
                        </div>
                        <h3 class="feature-title">حملات تسويقية</h3>
                        <p class="feature-description">
                            إنشاء وإدارة حملات تسويقية مستهدفة مع تتبع النتائج والتحليل المتقدم
                        </p>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <h3 class="feature-title">أمان متقدم</h3>
                        <p class="feature-description">
                            حماية عالية المستوى مع تشفير البيانات وسجلات شاملة لجميع الأنشطة
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- قسم حول النظام -->
    <section id="about" class="py-5" style="background: #f8f9fa;">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <h2 class="display-5 fw-bold text-dark mb-4">لماذا نظامنا؟</h2>
                    <p class="lead mb-4">
                        نظام أتمتة WhatsApp Business المطور خصيصاً للشركات العربية، يجمع بين السهولة والقوة في حل واحد متكامل.
                    </p>
                    
                    <div class="row">
                        <div class="col-sm-6 mb-3">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-check-circle text-success me-3 fs-4"></i>
                                <span>سهل الاستخدام</span>
                            </div>
                        </div>
                        <div class="col-sm-6 mb-3">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-check-circle text-success me-3 fs-4"></i>
                                <span>دعم كامل للعربية</span>
                            </div>
                        </div>
                        <div class="col-sm-6 mb-3">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-check-circle text-success me-3 fs-4"></i>
                                <span>تحديثات مستمرة</span>
                            </div>
                        </div>
                        <div class="col-sm-6 mb-3">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-check-circle text-success me-3 fs-4"></i>
                                <span>دعم فني متميز</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-6">
                    <div class="text-center">
                        <i class="fab fa-whatsapp" style="font-size: 15rem; color: #25D366; opacity: 0.1;"></i>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- التذييل -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>نظام أتمتة WhatsApp Business</h5>
                    <p class="mb-0">حل شامل لأتمتة WhatsApp Business مع ميزات متقدمة</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">
                        &copy; 2024 جميع الحقوق محفوظة
                    </p>
                    <p class="mb-0">
                        <small>تم التطوير بـ <i class="fas fa-heart text-danger"></i> للشركات العربية</small>
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- زر WhatsApp العائم -->
    <a href="https://wa.me/966XXXXXXXXX" class="whatsapp-float" target="_blank" title="تواصل معنا عبر WhatsApp">
        <i class="fab fa-whatsapp"></i>
    </a>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تأثير التمرير السلس
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // تأثير الظهور عند التمرير
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // تطبيق التأثير على البطاقات
        document.querySelectorAll('.feature-card').forEach(card => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';
            card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(card);
        });

        // عداد الإحصائيات المتحرك
        function animateCounter(element, target) {
            let current = 0;
            const increment = target / 100;
            const timer = setInterval(() => {
                current += increment;
                if (current >= target) {
                    current = target;
                    clearInterval(timer);
                }
                element.textContent = Math.floor(current);
            }, 20);
        }

        // تفعيل العدادات عند الوصول للقسم
        const statsSection = document.querySelector('.stats-section');
        let statsAnimated = false;

        const statsObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting && !statsAnimated) {
                    statsAnimated = true;
                    // يمكن إضافة عدادات متحركة هنا إذا لزم الأمر
                }
            });
        });

        if (statsSection) {
            statsObserver.observe(statsSection);
        }
    </script>
</body>
</html>
