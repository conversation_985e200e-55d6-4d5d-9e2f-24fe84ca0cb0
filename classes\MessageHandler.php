<?php
/**
 * فئة معالج الرسائل
 * Message Handler Class
 */

class MessageHandler {
    private $database;
    private $whatsappAPI;
    
    public function __construct($database, $whatsappAPI) {
        $this->database = $database;
        $this->whatsappAPI = $whatsappAPI;
    }
    
    /**
     * معالجة الرسالة الواردة
     * Process incoming message
     */
    public function processIncomingMessage($messageData) {
        try {
            $phoneNumber = $messageData['from'];
            $messageId = $messageData['id'];
            $messageType = $messageData['type'];
            $timestamp = $messageData['timestamp'];
            
            // استخراج محتوى الرسالة حسب النوع
            $content = $this->extractMessageContent($messageData, $messageType);
            
            // البحث عن جهة الاتصال أو إنشاؤها
            $contactId = $this->getOrCreateContact($phoneNumber, $messageData);
            
            // تسجيل الرسالة في قاعدة البيانات
            $this->logIncomingMessage($contactId, $messageId, $messageType, $content, $timestamp);
            
            // معالجة الردود التلقائية
            $this->processAutoReplies($phoneNumber, $content, $messageType);
            
            // تحديث الإحصائيات
            $this->updateDailyStats('messages_received');
            
            return true;
            
        } catch (Exception $e) {
            $this->logError("Error processing incoming message: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * استخراج محتوى الرسالة
     * Extract message content
     */
    private function extractMessageContent($messageData, $type) {
        switch ($type) {
            case 'text':
                return $messageData['text']['body'];
                
            case 'image':
                return $messageData['image']['caption'] ?? 'صورة';
                
            case 'document':
                return $messageData['document']['filename'] ?? 'مستند';
                
            case 'audio':
                return 'رسالة صوتية';
                
            case 'video':
                return $messageData['video']['caption'] ?? 'فيديو';
                
            case 'location':
                return 'موقع جغرافي';
                
            case 'contacts':
                return 'جهة اتصال';
                
            default:
                return 'رسالة غير مدعومة';
        }
    }
    
    /**
     * الحصول على جهة الاتصال أو إنشاؤها
     * Get or create contact
     */
    private function getOrCreateContact($phoneNumber, $messageData) {
        $contact = $this->database->fetchOne(
            "SELECT id FROM contacts WHERE phone_number = ?",
            [$phoneNumber]
        );
        
        if (!$contact) {
            $contactData = [
                'phone_number' => $phoneNumber,
                'whatsapp_id' => $messageData['from'],
                'name' => $messageData['profile']['name'] ?? null,
                'last_message_at' => date('Y-m-d H:i:s'),
                'created_at' => date('Y-m-d H:i:s')
            ];
            
            $contactId = $this->database->insert('contacts', $contactData);
            $this->updateDailyStats('new_contacts');
            
            return $contactId;
        } else {
            // تحديث آخر رسالة
            $this->database->update(
                'contacts',
                ['last_message_at' => date('Y-m-d H:i:s')],
                'id = ?',
                [$contact['id']]
            );
            
            return $contact['id'];
        }
    }
    
    /**
     * تسجيل الرسالة الواردة
     * Log incoming message
     */
    private function logIncomingMessage($contactId, $messageId, $type, $content, $timestamp) {
        $messageData = [
            'message_id' => $messageId,
            'contact_id' => $contactId,
            'direction' => 'incoming',
            'message_type' => $type,
            'content' => $content,
            'status' => 'received',
            'created_at' => date('Y-m-d H:i:s', $timestamp)
        ];
        
        return $this->database->insert('messages', $messageData);
    }
    
    /**
     * معالجة الردود التلقائية
     * Process auto replies
     */
    private function processAutoReplies($phoneNumber, $content, $messageType) {
        // التحقق من تفعيل الردود التلقائية
        $autoReplyEnabled = getSetting('auto_reply_enabled', '1');
        if ($autoReplyEnabled !== '1') {
            return;
        }
        
        // التحقق من ساعات العمل
        if (!$this->isBusinessHours()) {
            $outOfHoursMessage = getSetting('out_of_hours_message', DEFAULT_OUT_OF_HOURS_MESSAGE);
            $this->whatsappAPI->sendTextMessage($phoneNumber, $outOfHoursMessage, true);
            return;
        }
        
        // البحث عن رد تلقائي مناسب
        $autoReply = $this->findMatchingAutoReply($content);
        
        if ($autoReply) {
            // إرسال الرد التلقائي
            $this->whatsappAPI->sendTextMessage($phoneNumber, $autoReply['response_text'], true);
            
            // تحديث عداد الاستخدام
            $this->database->update(
                'auto_replies',
                ['usage_count' => $autoReply['usage_count'] + 1],
                'id = ?',
                [$autoReply['id']]
            );
            
            $this->updateDailyStats('auto_replies_sent');
        } else {
            // إرسال رسالة الترحيب الافتراضية للرسائل الجديدة
            if ($this->isFirstMessage($phoneNumber)) {
                $welcomeMessage = getSetting('welcome_message', DEFAULT_WELCOME_MESSAGE);
                $this->whatsappAPI->sendTextMessage($phoneNumber, $welcomeMessage, true);
            }
        }
    }
    
    /**
     * البحث عن رد تلقائي مناسب
     * Find matching auto reply
     */
    private function findMatchingAutoReply($content) {
        $autoReplies = $this->database->fetchAll(
            "SELECT * FROM auto_replies WHERE is_active = 1 ORDER BY priority DESC"
        );
        
        foreach ($autoReplies as $reply) {
            $keywords = json_decode($reply['keywords'], true);
            
            if ($this->matchesKeywords($content, $keywords)) {
                return $reply;
            }
        }
        
        return null;
    }
    
    /**
     * التحقق من تطابق الكلمات المفتاحية
     * Check keyword matching
     */
    private function matchesKeywords($content, $keywords) {
        $content = mb_strtolower($content);
        
        foreach ($keywords as $keyword) {
            $keyword = mb_strtolower($keyword);
            
            if (mb_strpos($content, $keyword) !== false) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * التحقق من ساعات العمل
     * Check business hours
     */
    private function isBusinessHours() {
        $startTime = getSetting('business_hours_start', '09:00');
        $endTime = getSetting('business_hours_end', '18:00');
        $currentTime = date('H:i');
        
        return ($currentTime >= $startTime && $currentTime <= $endTime);
    }
    
    /**
     * التحقق من كون الرسالة الأولى
     * Check if first message
     */
    private function isFirstMessage($phoneNumber) {
        $messageCount = $this->database->fetchOne(
            "SELECT COUNT(*) as count FROM messages m 
             JOIN contacts c ON m.contact_id = c.id 
             WHERE c.phone_number = ? AND m.direction = 'incoming'",
            [$phoneNumber]
        );
        
        return $messageCount['count'] <= 1;
    }
    
    /**
     * تحديث الإحصائيات اليومية
     * Update daily statistics
     */
    private function updateDailyStats($field) {
        $today = date('Y-m-d');
        
        $existing = $this->database->fetchOne(
            "SELECT id FROM daily_stats WHERE date = ?",
            [$today]
        );
        
        if ($existing) {
            $this->database->query(
                "UPDATE daily_stats SET {$field} = {$field} + 1 WHERE date = ?",
                [$today]
            );
        } else {
            $this->database->insert('daily_stats', [
                'date' => $today,
                $field => 1
            ]);
        }
    }
    
    /**
     * معالجة تحديثات حالة الرسالة
     * Process message status updates
     */
    public function processStatusUpdate($statusData) {
        try {
            $messageId = $statusData['id'];
            $status = $statusData['status'];
            $timestamp = $statusData['timestamp'];
            
            $updateData = ['status' => $status];
            
            switch ($status) {
                case 'delivered':
                    $updateData['delivered_at'] = date('Y-m-d H:i:s', $timestamp);
                    break;
                case 'read':
                    $updateData['read_at'] = date('Y-m-d H:i:s', $timestamp);
                    break;
            }
            
            $this->database->update(
                'messages',
                $updateData,
                'message_id = ?',
                [$messageId]
            );
            
            return true;
            
        } catch (Exception $e) {
            $this->logError("Error processing status update: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * تسجيل الأخطاء
     * Log errors
     */
    private function logError($message) {
        $logFile = LOG_PATH . 'message_handler_' . date('Y-m-d') . '.log';
        $timestamp = date('Y-m-d H:i:s');
        file_put_contents($logFile, "[$timestamp] ERROR: $message\n", FILE_APPEND | LOCK_EX);
    }
}
?>
