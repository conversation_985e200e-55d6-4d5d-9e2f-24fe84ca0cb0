<?php
/**
 * صفحة البدء السريع والاختبار
 * Quick Start and Testing Page
 */

// التحقق من وجود ملف التثبيت
$installationNeeded = !file_exists('install_complete.lock') || !file_exists('.env');
$configExists = file_exists('config/config.php');

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>البدء السريع - نظام أتمتة WhatsApp Business</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #25D366 0%, #128C7E 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .container-custom {
            max-width: 900px;
            margin: 50px auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .header {
            background: #25D366;
            color: white;
            padding: 30px;
            text-align: center;
        }
        .content {
            padding: 40px;
        }
        .status-card {
            border: none;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status-success {
            border-right: 4px solid #28a745;
        }
        .status-warning {
            border-right: 4px solid #ffc107;
        }
        .status-danger {
            border-right: 4px solid #dc3545;
        }
        .btn-custom {
            border-radius: 25px;
            padding: 10px 25px;
            font-weight: 600;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        .feature-list li:last-child {
            border-bottom: none;
        }
        .feature-list i {
            width: 20px;
            text-align: center;
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <div class="container-custom">
        <div class="header">
            <i class="fab fa-whatsapp fa-3x mb-3"></i>
            <h2>نظام أتمتة WhatsApp Business</h2>
            <p class="mb-0">البدء السريع والاختبار</p>
        </div>
        
        <div class="content">
            <!-- حالة النظام -->
            <div class="row mb-4">
                <div class="col-12">
                    <h4><i class="fas fa-chart-line text-primary"></i> حالة النظام</h4>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="card status-card <?php echo $installationNeeded ? 'status-warning' : 'status-success'; ?>">
                        <div class="card-body">
                            <h6 class="card-title">
                                <i class="fas fa-<?php echo $installationNeeded ? 'exclamation-triangle text-warning' : 'check-circle text-success'; ?>"></i>
                                حالة التثبيت
                            </h6>
                            <p class="card-text">
                                <?php if ($installationNeeded): ?>
                                    النظام يحتاج إلى تثبيت أو إعداد
                                <?php else: ?>
                                    النظام مثبت ومُعد بنجاح
                                <?php endif; ?>
                            </p>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card status-card <?php echo $configExists ? 'status-success' : 'status-danger'; ?>">
                        <div class="card-body">
                            <h6 class="card-title">
                                <i class="fas fa-<?php echo $configExists ? 'check-circle text-success' : 'times-circle text-danger'; ?>"></i>
                                ملف التكوين
                            </h6>
                            <p class="card-text">
                                <?php if ($configExists): ?>
                                    ملف التكوين موجود وجاهز
                                <?php else: ?>
                                    ملف التكوين غير موجود
                                <?php endif; ?>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- فحص المتطلبات -->
            <div class="row mb-4">
                <div class="col-12">
                    <h4><i class="fas fa-clipboard-check text-primary"></i> فحص المتطلبات</h4>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="card status-card">
                        <div class="card-body">
                            <h6 class="card-title">متطلبات PHP</h6>
                            <ul class="feature-list">
                                <li>
                                    <i class="fas fa-<?php echo version_compare(PHP_VERSION, '8.0.0', '>=') ? 'check text-success' : 'times text-danger'; ?>"></i>
                                    PHP 8.0+ (الحالي: <?php echo PHP_VERSION; ?>)
                                </li>
                                <li>
                                    <i class="fas fa-<?php echo extension_loaded('pdo_mysql') ? 'check text-success' : 'times text-danger'; ?>"></i>
                                    PDO MySQL
                                </li>
                                <li>
                                    <i class="fas fa-<?php echo extension_loaded('curl') ? 'check text-success' : 'times text-danger'; ?>"></i>
                                    cURL
                                </li>
                                <li>
                                    <i class="fas fa-<?php echo extension_loaded('json') ? 'check text-success' : 'times text-danger'; ?>"></i>
                                    JSON
                                </li>
                                <li>
                                    <i class="fas fa-<?php echo extension_loaded('mbstring') ? 'check text-success' : 'times text-danger'; ?>"></i>
                                    mbstring
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card status-card">
                        <div class="card-body">
                            <h6 class="card-title">صلاحيات المجلدات</h6>
                            <ul class="feature-list">
                                <li>
                                    <i class="fas fa-<?php echo is_writable('logs/') || !file_exists('logs/') ? 'check text-success' : 'times text-danger'; ?>"></i>
                                    مجلد logs/
                                </li>
                                <li>
                                    <i class="fas fa-<?php echo is_writable('uploads/') || !file_exists('uploads/') ? 'check text-success' : 'times text-danger'; ?>"></i>
                                    مجلد uploads/
                                </li>
                                <li>
                                    <i class="fas fa-<?php echo is_writable('reports/') || !file_exists('reports/') ? 'check text-success' : 'times text-danger'; ?>"></i>
                                    مجلد reports/
                                </li>
                                <li>
                                    <i class="fas fa-<?php echo is_writable('backups/') || !file_exists('backups/') ? 'check text-success' : 'times text-danger'; ?>"></i>
                                    مجلد backups/
                                </li>
                                <li>
                                    <i class="fas fa-<?php echo is_writable('config/') || is_writable('.') ? 'check text-success' : 'times text-danger'; ?>"></i>
                                    مجلد config/
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- الإجراءات السريعة -->
            <div class="row mb-4">
                <div class="col-12">
                    <h4><i class="fas fa-rocket text-primary"></i> الإجراءات السريعة</h4>
                </div>
            </div>
            
            <div class="row">
                <?php if ($installationNeeded): ?>
                    <div class="col-md-4 mb-3">
                        <div class="card status-card status-warning">
                            <div class="card-body text-center">
                                <i class="fas fa-download fa-2x text-warning mb-3"></i>
                                <h6>بدء التثبيت</h6>
                                <p class="small">تثبيت النظام عبر المعالج الرسومي</p>
                                <a href="install/" class="btn btn-warning btn-custom">
                                    <i class="fas fa-play"></i> ابدأ التثبيت
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <div class="card status-card">
                            <div class="card-body text-center">
                                <i class="fas fa-terminal fa-2x text-info mb-3"></i>
                                <h6>تثبيت سطر الأوامر</h6>
                                <p class="small">تثبيت سريع من Terminal</p>
                                <code class="small">php install/cli_install.php</code>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <div class="card status-card">
                            <div class="card-body text-center">
                                <i class="fas fa-magic fa-2x text-success mb-3"></i>
                                <h6>التثبيت السريع</h6>
                                <p class="small">تثبيت بأمر واحد</p>
                                <code class="small">./install.sh</code>
                            </div>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="col-md-4 mb-3">
                        <div class="card status-card status-success">
                            <div class="card-body text-center">
                                <i class="fas fa-tachometer-alt fa-2x text-success mb-3"></i>
                                <h6>لوحة التحكم</h6>
                                <p class="small">الوصول إلى لوحة الإدارة</p>
                                <a href="admin/" class="btn btn-success btn-custom" target="_blank">
                                    <i class="fas fa-external-link-alt"></i> فتح اللوحة
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <div class="card status-card">
                            <div class="card-body text-center">
                                <i class="fas fa-chart-bar fa-2x text-info mb-3"></i>
                                <h6>التقارير</h6>
                                <p class="small">عرض التقارير والإحصائيات</p>
                                <a href="admin/reports.php" class="btn btn-info btn-custom" target="_blank">
                                    <i class="fas fa-chart-line"></i> التقارير
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <div class="card status-card">
                            <div class="card-body text-center">
                                <i class="fas fa-mouse-pointer fa-2x text-primary mb-3"></i>
                                <h6>الأزرار التفاعلية</h6>
                                <p class="small">إدارة الأزرار التفاعلية</p>
                                <a href="admin/interactive_buttons.php" class="btn btn-primary btn-custom" target="_blank">
                                    <i class="fas fa-hand-pointer"></i> الأزرار
                                </a>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
            
            <!-- معلومات النظام -->
            <div class="row">
                <div class="col-12">
                    <div class="card status-card">
                        <div class="card-body">
                            <h6 class="card-title">
                                <i class="fas fa-info-circle text-primary"></i>
                                معلومات النظام
                            </h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <ul class="feature-list">
                                        <li><strong>إصدار PHP:</strong> <?php echo PHP_VERSION; ?></li>
                                        <li><strong>نظام التشغيل:</strong> <?php echo PHP_OS; ?></li>
                                        <li><strong>الذاكرة المتاحة:</strong> <?php echo ini_get('memory_limit'); ?></li>
                                        <li><strong>الحد الأقصى للرفع:</strong> <?php echo ini_get('upload_max_filesize'); ?></li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <ul class="feature-list">
                                        <li><strong>المنطقة الزمنية:</strong> <?php echo date_default_timezone_get(); ?></li>
                                        <li><strong>الوقت الحالي:</strong> <?php echo date('Y-m-d H:i:s'); ?></li>
                                        <li><strong>مجلد العمل:</strong> <?php echo getcwd(); ?></li>
                                        <li><strong>خادم الويب:</strong> <?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'غير محدد'; ?></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- روابط مفيدة -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="text-center">
                        <h6>روابط مفيدة</h6>
                        <div class="d-flex justify-content-center gap-3 flex-wrap">
                            <a href="README.md" class="btn btn-outline-primary btn-sm" target="_blank">
                                <i class="fas fa-book"></i> التوثيق
                            </a>
                            <a href="install/README.md" class="btn btn-outline-info btn-sm" target="_blank">
                                <i class="fas fa-download"></i> دليل التثبيت
                            </a>
                            <a href="examples/" class="btn btn-outline-success btn-sm" target="_blank">
                                <i class="fas fa-code"></i> أمثلة الكود
                            </a>
                            <a href="https://developers.facebook.com/docs/whatsapp" class="btn btn-outline-warning btn-sm" target="_blank">
                                <i class="fab fa-facebook"></i> WhatsApp API
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تحديث الصفحة كل 30 ثانية لمراقبة التغييرات
        setTimeout(() => {
            location.reload();
        }, 30000);
        
        // إظهار رسالة ترحيب
        document.addEventListener('DOMContentLoaded', function() {
            <?php if (!$installationNeeded): ?>
                console.log('🎉 مرحباً بك في نظام أتمتة WhatsApp Business!');
                console.log('📱 النظام جاهز للاستخدام');
                console.log('🚀 ابدأ من لوحة التحكم: admin/');
            <?php else: ?>
                console.log('⚠️ النظام يحتاج إلى تثبيت');
                console.log('🔧 ابدأ التثبيت من: install/');
            <?php endif; ?>
        });
    </script>
</body>
</html>
