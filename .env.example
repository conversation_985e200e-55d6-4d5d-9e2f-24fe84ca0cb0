# ملف الإعدادات النموذجي لنظام أتمتة WhatsApp Business
# Example Configuration File for WhatsApp Business Automation System

# =============================================================================
# إعدادات التطبيق الأساسية
# Basic Application Settings
# =============================================================================

# رابط الموقع الأساسي (بدون / في النهاية)
APP_URL=https://yourdomain.com

# وضع التطوير (true للتطوير، false للإنتاج)
APP_DEBUG=false

# اسم التطبيق
APP_NAME="WhatsApp Business Automation"

# إصدار التطبيق
APP_VERSION=1.0.0

# =============================================================================
# إعدادات قاعدة البيانات
# Database Configuration
# =============================================================================

# عنوان خادم قاعدة البيانات
DB_HOST=localhost

# منفذ قاعدة البيانات (3306 هو الافتراضي لـ MySQL)
DB_PORT=3306

# اسم قاعدة البيانات
DB_NAME=whatsapp_automation

# اسم مستخدم قاعدة البيانات
DB_USERNAME=your_db_username

# كلمة مرور قاعدة البيانات
DB_PASSWORD=your_db_password

# ترميز قاعدة البيانات
DB_CHARSET=utf8mb4

# =============================================================================
# إعدادات WhatsApp Business API
# WhatsApp Business API Configuration
# =============================================================================

# رمز الوصول الدائم من Facebook Developers
WHATSAPP_ACCESS_TOKEN=your_permanent_access_token_here

# معرف رقم الهاتف في WhatsApp Business Platform
WHATSAPP_PHONE_NUMBER_ID=your_phone_number_id_here

# رمز التحقق من Webhook (يجب أن يكون نفس الرمز في Facebook)
WHATSAPP_WEBHOOK_VERIFY_TOKEN=your_custom_webhook_verify_token

# إصدار WhatsApp API (افتراضي: v18.0)
WHATSAPP_API_VERSION=v18.0

# الحد الأقصى لعدد الرسائل في الدقيقة الواحدة
MAX_MESSAGES_PER_MINUTE=30

# الحد الأقصى لعدد الرسائل في الساعة الواحدة
MAX_MESSAGES_PER_HOUR=1000

# =============================================================================
# إعدادات الأمان والتشفير
# Security and Encryption Settings
# =============================================================================

# مفتاح JWT للمصادقة (32 حرف عشوائي)
JWT_SECRET=your_32_character_random_jwt_secret_key_here

# مفتاح التشفير العام (32 حرف عشوائي)
ENCRYPTION_KEY=your_32_character_encryption_key_here

# مدة صلاحية الجلسة بالدقائق (افتراضي: 1440 = 24 ساعة)
SESSION_LIFETIME=1440

# تفعيل HTTPS فقط للكوكيز
SECURE_COOKIES=true

# =============================================================================
# إعدادات النظام العامة
# General System Settings
# =============================================================================

# المنطقة الزمنية
TIMEZONE=Asia/Riyadh

# اللغة الافتراضية
DEFAULT_LANGUAGE=ar

# تفعيل وضع الصيانة
MAINTENANCE_MODE=false

# ساعات العمل - بداية (24 ساعة)
BUSINESS_HOURS_START=09:00

# ساعات العمل - نهاية (24 ساعة)
BUSINESS_HOURS_END=18:00

# أيام العمل (1=الاثنين، 7=الأحد)
BUSINESS_DAYS=1,2,3,4,5

# =============================================================================
# إعدادات السجلات والتشخيص
# Logging and Debugging Settings
# =============================================================================

# مستوى السجلات (debug, info, warning, error)
LOG_LEVEL=info

# تفعيل سجلات قاعدة البيانات
LOG_DATABASE_QUERIES=false

# تفعيل سجلات API
LOG_API_REQUESTS=true

# الحد الأقصى لحجم ملف السجل بالميجابايت
MAX_LOG_FILE_SIZE=10

# عدد أيام الاحتفاظ بالسجلات
LOG_RETENTION_DAYS=30

# =============================================================================
# إعدادات التخزين المؤقت
# Cache Configuration
# =============================================================================

# تفعيل التخزين المؤقت
CACHE_ENABLED=true

# نوع التخزين المؤقت (file, redis, memcached)
CACHE_DRIVER=file

# مدة التخزين المؤقت الافتراضية بالثواني
CACHE_DEFAULT_TTL=3600

# =============================================================================
# إعدادات الملفات والرفع
# File Upload Settings
# =============================================================================

# الحد الأقصى لحجم الملف المرفوع بالميجابايت
MAX_UPLOAD_SIZE=10

# أنواع الملفات المسموحة
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx,mp3,mp4,wav

# مجلد الرفع
UPLOAD_PATH=uploads/

# =============================================================================
# إعدادات البريد الإلكتروني
# Email Configuration
# =============================================================================

# تفعيل إشعارات البريد الإلكتروني
EMAIL_NOTIFICATIONS=false

# خادم SMTP
MAIL_HOST=smtp.gmail.com

# منفذ SMTP
MAIL_PORT=587

# اسم المستخدم
MAIL_USERNAME=<EMAIL>

# كلمة المرور
MAIL_PASSWORD=your_email_password

# التشفير (tls, ssl)
MAIL_ENCRYPTION=tls

# البريد الإلكتروني للمرسل
MAIL_FROM_ADDRESS=<EMAIL>

# اسم المرسل
MAIL_FROM_NAME="WhatsApp Automation System"

# =============================================================================
# إعدادات النسخ الاحتياطي
# Backup Configuration
# =============================================================================

# تفعيل النسخ الاحتياطي التلقائي
AUTO_BACKUP_ENABLED=true

# مجلد النسخ الاحتياطية
BACKUP_PATH=backups/

# عدد أيام الاحتفاظ بالنسخ الاحتياطية
BACKUP_RETENTION_DAYS=30

# تفعيل ضغط النسخ الاحتياطية
BACKUP_COMPRESSION=true

# =============================================================================
# إعدادات التقارير
# Reports Configuration
# =============================================================================

# مجلد التقارير
REPORTS_PATH=reports/

# تفعيل التقارير التلقائية
AUTO_REPORTS_ENABLED=true

# تنسيق التقارير الافتراضي (pdf, excel, csv)
DEFAULT_REPORT_FORMAT=pdf

# =============================================================================
# إعدادات الإشعارات
# Notifications Settings
# =============================================================================

# تفعيل الإشعارات العامة
NOTIFICATIONS_ENABLED=true

# تفعيل إشعارات الأخطاء
ERROR_NOTIFICATIONS=true

# تفعيل إشعارات النظام
SYSTEM_NOTIFICATIONS=true

# البريد الإلكتروني للإشعارات المهمة
ADMIN_EMAIL=<EMAIL>

# =============================================================================
# إعدادات الأداء
# Performance Settings
# =============================================================================

# تفعيل ضغط الاستجابات
RESPONSE_COMPRESSION=true

# تفعيل التخزين المؤقت للصفحات
PAGE_CACHE_ENABLED=false

# مدة التخزين المؤقت للصفحات بالثواني
PAGE_CACHE_TTL=300

# =============================================================================
# إعدادات التطوير والاختبار
# Development and Testing Settings
# =============================================================================

# تفعيل وضع التطوير المتقدم
ADVANCED_DEBUG=false

# تفعيل عرض الأخطاء
DISPLAY_ERRORS=false

# تفعيل وضع الاختبار
TESTING_MODE=false

# قاعدة بيانات الاختبار
TEST_DB_NAME=whatsapp_automation_test

# =============================================================================
# إعدادات مخصصة
# Custom Settings
# =============================================================================

# اسم الشركة
COMPANY_NAME="Your Company Name"

# شعار الشركة
COMPANY_LOGO=assets/images/logo.png

# معلومات الاتصال
COMPANY_PHONE="+966-XX-XXX-XXXX"
COMPANY_EMAIL="<EMAIL>"
COMPANY_ADDRESS="Your Company Address"

# روابط وسائل التواصل الاجتماعي
SOCIAL_FACEBOOK=""
SOCIAL_TWITTER=""
SOCIAL_INSTAGRAM=""
SOCIAL_LINKEDIN=""

# =============================================================================
# ملاحظات مهمة
# Important Notes
# =============================================================================

# 1. لا تشارك هذا الملف مع أي شخص - يحتوي على معلومات حساسة
# 2. استخدم كلمات مرور قوية ومفاتيح تشفير عشوائية
# 3. تأكد من تفعيل HTTPS في الإنتاج
# 4. راجع الإعدادات بانتظام وحدثها حسب الحاجة
# 5. احتفظ بنسخة احتياطية من هذا الملف في مكان آمن

# =============================================================================
# إعدادات إضافية (اختيارية)
# Additional Settings (Optional)
# =============================================================================

# تفعيل وضع المطور
DEVELOPER_MODE=false

# عرض معلومات النظام
SHOW_SYSTEM_INFO=false

# تفعيل API الخارجي
EXTERNAL_API_ENABLED=false

# مفتاح API الخارجي
EXTERNAL_API_KEY=""
