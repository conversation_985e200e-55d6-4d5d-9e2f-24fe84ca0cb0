/* نمط لوحة التحكم */
/* <PERSON><PERSON> Dashboard Styles */

:root {
    --whatsapp-green: #25D366;
    --whatsapp-dark: #128C7E;
    --whatsapp-light: #DCF8C6;
    --sidebar-width: 250px;
}

body {
    font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

/* الشريط الجانبي */
.sidebar {
    position: fixed;
    top: 0;
    bottom: 0;
    right: 0;
    z-index: 100;
    padding: 48px 0 0;
    box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
    width: var(--sidebar-width);
}

.sidebar-sticky {
    position: relative;
    top: 0;
    height: calc(100vh - 48px);
    padding-top: .5rem;
    overflow-x: hidden;
    overflow-y: auto;
}

.sidebar .nav-link {
    font-weight: 500;
    color: #333;
    padding: 12px 20px;
    border-radius: 0;
    transition: all 0.3s ease;
}

.sidebar .nav-link:hover {
    color: var(--whatsapp-green);
    background-color: rgba(37, 211, 102, 0.1);
}

.sidebar .nav-link.active {
    color: var(--whatsapp-green);
    background-color: rgba(37, 211, 102, 0.15);
    border-right: 3px solid var(--whatsapp-green);
}

.sidebar .nav-link i {
    margin-left: 10px;
    width: 20px;
    text-align: center;
}

.sidebar-heading {
    font-size: .75rem;
    text-transform: uppercase;
    font-weight: 600;
    color: #6c757d;
    padding: 0 20px;
    margin-top: 20px;
    margin-bottom: 10px;
}

/* المحتوى الرئيسي */
main {
    margin-right: var(--sidebar-width);
    padding-top: 20px;
}

/* البطاقات */
.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: box-shadow 0.15s ease-in-out;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    font-weight: 600;
    color: #495057;
}

/* الأزرار */
.btn-primary {
    background-color: var(--whatsapp-green);
    border-color: var(--whatsapp-green);
}

.btn-primary:hover {
    background-color: var(--whatsapp-dark);
    border-color: var(--whatsapp-dark);
}

.btn-outline-primary {
    color: var(--whatsapp-green);
    border-color: var(--whatsapp-green);
}

.btn-outline-primary:hover {
    background-color: var(--whatsapp-green);
    border-color: var(--whatsapp-green);
}

/* الجداول */
.table {
    border-radius: 10px;
    overflow: hidden;
}

.table thead th {
    border-top: none;
    background-color: #f8f9fa;
    font-weight: 600;
    color: #495057;
}

.table-hover tbody tr:hover {
    background-color: rgba(37, 211, 102, 0.05);
}

/* الشارات */
.badge {
    font-size: 0.75em;
    padding: 0.5em 0.75em;
}

/* الإحصائيات */
.border-right-primary {
    border-right: 0.25rem solid var(--whatsapp-green) !important;
}

.border-right-success {
    border-right: 0.25rem solid #28a745 !important;
}

.border-right-info {
    border-right: 0.25rem solid #17a2b8 !important;
}

.border-right-warning {
    border-right: 0.25rem solid #ffc107 !important;
}

/* الأزرار التفاعلية */
.button-preview {
    background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
    border-radius: 15px;
    padding: 20px;
    margin: 10px 0;
}

.whatsapp-message {
    background: white;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    position: relative;
    max-width: 300px;
    margin: 0 auto;
}

.whatsapp-message::before {
    content: '';
    position: absolute;
    top: 15px;
    right: -10px;
    width: 0;
    height: 0;
    border-left: 10px solid white;
    border-top: 10px solid transparent;
    border-bottom: 10px solid transparent;
}

.message-header {
    font-weight: bold;
    color: var(--whatsapp-green);
    margin-bottom: 10px;
    font-size: 1.1em;
}

.message-body {
    margin-bottom: 15px;
    color: #333;
    line-height: 1.4;
}

.interactive-buttons {
    margin-bottom: 10px;
}

.interactive-buttons .btn {
    display: block;
    width: 100%;
    text-align: center;
    margin-bottom: 8px;
    border-radius: 20px;
    font-size: 0.9em;
    padding: 8px 15px;
    border: 1px solid #e0e0e0;
    background: #f8f9fa;
    color: #333;
    transition: all 0.3s ease;
}

.interactive-buttons .btn:hover {
    background: var(--whatsapp-green);
    color: white;
    border-color: var(--whatsapp-green);
    transform: translateY(-2px);
}

.message-footer {
    font-size: 0.85em;
    color: #666;
    text-align: center;
    font-style: italic;
}

/* قائمة تفاعلية */
.interactive-list {
    border: 1px solid #e0e0e0;
    border-radius: 10px;
    overflow: hidden;
    margin-top: 10px;
}

.list-item {
    padding: 12px 15px;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.list-item:hover {
    background-color: rgba(37, 211, 102, 0.1);
}

.list-item:last-child {
    border-bottom: none;
}

.list-item-title {
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
}

.list-item-description {
    font-size: 0.9em;
    color: #666;
}

/* الرسوم البيانية */
.chart-container {
    position: relative;
    height: 300px;
    margin: 20px 0;
}

/* التنبيهات */
.alert {
    border-radius: 10px;
    border: none;
    padding: 15px 20px;
}

.alert-success {
    background-color: rgba(40, 167, 69, 0.1);
    color: #155724;
}

.alert-danger {
    background-color: rgba(220, 53, 69, 0.1);
    color: #721c24;
}

.alert-warning {
    background-color: rgba(255, 193, 7, 0.1);
    color: #856404;
}

.alert-info {
    background-color: rgba(23, 162, 184, 0.1);
    color: #0c5460;
}

/* النماذج */
.form-control {
    border-radius: 8px;
    border: 1px solid #ced4da;
    padding: 10px 15px;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-control:focus {
    border-color: var(--whatsapp-green);
    box-shadow: 0 0 0 0.2rem rgba(37, 211, 102, 0.25);
}

.form-select {
    border-radius: 8px;
    border: 1px solid #ced4da;
    padding: 10px 15px;
}

.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
}

/* التبديل */
.nav-tabs .nav-link {
    border-radius: 10px 10px 0 0;
    border: none;
    color: #6c757d;
    font-weight: 500;
}

.nav-tabs .nav-link.active {
    background-color: var(--whatsapp-green);
    color: white;
}

/* الشريط العلوي */
.navbar-brand {
    font-weight: bold;
    color: white !important;
}

.navbar-dark .navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.8);
}

.navbar-dark .navbar-nav .nav-link:hover {
    color: white;
}

/* التحميل */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .sidebar {
        position: relative;
        width: 100%;
        height: auto;
        padding: 0;
    }
    
    main {
        margin-right: 0;
    }
    
    .whatsapp-message {
        max-width: 100%;
    }
}

/* تحسينات إضافية */
.text-whatsapp {
    color: var(--whatsapp-green) !important;
}

.bg-whatsapp {
    background-color: var(--whatsapp-green) !important;
}

.border-whatsapp {
    border-color: var(--whatsapp-green) !important;
}

/* أيقونات الحالة */
.status-online {
    color: #28a745;
}

.status-offline {
    color: #6c757d;
}

.status-busy {
    color: #ffc107;
}

.status-error {
    color: #dc3545;
}

/* تأثيرات الحركة */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.pulse {
    animation: pulse 2s infinite;
}

/* تحسينات الطباعة */
@media print {
    .sidebar,
    .navbar,
    .btn,
    .alert {
        display: none !important;
    }
    
    main {
        margin-right: 0 !important;
    }
    
    .card {
        border: 1px solid #dee2e6 !important;
        box-shadow: none !important;
    }
}
