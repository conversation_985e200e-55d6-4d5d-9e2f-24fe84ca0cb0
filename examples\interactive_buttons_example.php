<?php
/**
 * مثال على استخدام الأزرار التفاعلية
 * Interactive Buttons Usage Example
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../classes/WhatsAppAPI.php';
require_once __DIR__ . '/../classes/InteractiveButtons.php';

// إنشاء مثيلات الفئات
$whatsappAPI = new WhatsAppAPI($database);
$interactiveButtons = new InteractiveButtons($database, $whatsappAPI);

// رقم الهاتف للاختبار (يجب تغييره لرقم حقيقي)
$testPhoneNumber = '+966501234567';

echo "🧪 اختبار الأزرار التفاعلية\n";
echo "================================\n\n";

try {
    // 1. إرسال القائمة الرئيسية
    echo "1️⃣ إرسال القائمة الرئيسية...\n";
    $result1 = $interactiveButtons->sendMainMenu($testPhoneNumber);
    echo $result1 ? "✅ تم الإرسال بنجاح\n" : "❌ فشل الإرسال\n";
    sleep(2);
    
    // 2. إرسال قائمة الخدمات
    echo "\n2️⃣ إرسال قائمة الخدمات...\n";
    $result2 = $interactiveButtons->sendServicesMenu($testPhoneNumber);
    echo $result2 ? "✅ تم الإرسال بنجاح\n" : "❌ فشل الإرسال\n";
    sleep(2);
    
    // 3. إرسال أزرار التقييم
    echo "\n3️⃣ إرسال أزرار التقييم...\n";
    $result3 = $interactiveButtons->sendRatingButtons($testPhoneNumber);
    echo $result3 ? "✅ تم الإرسال بنجاح\n" : "❌ فشل الإرسال\n";
    sleep(2);
    
    // 4. إرسال رسالة بأزرار مخصصة
    echo "\n4️⃣ إرسال أزرار مخصصة...\n";
    $customButtons = [
        ['id' => 'yes', 'title' => '✅ نعم'],
        ['id' => 'no', 'title' => '❌ لا'],
        ['id' => 'maybe', 'title' => '🤔 ربما']
    ];
    
    $result4 = $whatsappAPI->sendButtonMessage(
        $testPhoneNumber,
        'هل أنت مهتم بعروضنا الخاصة؟',
        $customButtons,
        'عرض خاص',
        'اختر إجابتك'
    );
    echo $result4 ? "✅ تم الإرسال بنجاح\n" : "❌ فشل الإرسال\n";
    sleep(2);
    
    // 5. إرسال قائمة مخصصة
    echo "\n5️⃣ إرسال قائمة مخصصة...\n";
    $customSections = [
        [
            'title' => 'المنتجات',
            'rows' => [
                [
                    'id' => 'product_1',
                    'title' => 'منتج رقم 1',
                    'description' => 'وصف المنتج الأول'
                ],
                [
                    'id' => 'product_2',
                    'title' => 'منتج رقم 2',
                    'description' => 'وصف المنتج الثاني'
                ]
            ]
        ]
    ];
    
    $result5 = $whatsappAPI->sendListMessage(
        $testPhoneNumber,
        'اختر المنتج الذي يهمك:',
        'عرض المنتجات',
        $customSections,
        'كتالوج المنتجات',
        'جميع المنتجات عالية الجودة'
    );
    echo $result5 ? "✅ تم الإرسال بنجاح\n" : "❌ فشل الإرسال\n";
    
    echo "\n🎉 انتهى الاختبار بنجاح!\n";
    echo "تحقق من WhatsApp لرؤية الرسائل التفاعلية\n";
    
} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage() . "\n";
}

echo "\n" . str_repeat('=', 50) . "\n";
echo "📋 معلومات مهمة:\n";
echo "• تأكد من صحة رقم الهاتف\n";
echo "• تأكد من صحة Access Token\n";
echo "• الأزرار التفاعلية تعمل فقط مع WhatsApp Business API\n";
echo "• الحد الأقصى للأزرار هو 3 في الرسالة الواحدة\n";
echo "• القوائم يمكن أن تحتوي على 10 عناصر كحد أقصى\n";

/**
 * مثال على معالجة النقر على الأزرار
 * Example of handling button clicks
 */
function simulateButtonClick() {
    global $interactiveButtons, $testPhoneNumber;
    
    echo "\n🖱️ محاكاة النقر على الأزرار:\n";
    
    // محاكاة النقر على زر الخدمات
    $interactiveButtons->handleButtonClick($testPhoneNumber, 'services', 'خدماتنا');
    echo "• تم النقر على زر 'خدماتنا'\n";
    
    // محاكاة النقر على زر التقييم
    $interactiveButtons->handleButtonClick($testPhoneNumber, 'rating_excellent', 'ممتاز');
    echo "• تم النقر على زر 'ممتاز'\n";
    
    // محاكاة النقر على زر مخصص
    $interactiveButtons->handleButtonClick($testPhoneNumber, 'yes', 'نعم');
    echo "• تم النقر على زر 'نعم'\n";
}

// تشغيل محاكاة النقر إذا تم تمرير معامل
if (isset($argv[1]) && $argv[1] === 'simulate') {
    simulateButtonClick();
}

/**
 * دالة لإنشاء أزرار ديناميكية
 * Function to create dynamic buttons
 */
function createDynamicButtons($phoneNumber, $context) {
    global $whatsappAPI;
    
    $buttons = [];
    
    switch ($context) {
        case 'restaurant':
            $buttons = [
                ['id' => 'menu', 'title' => '🍽️ القائمة'],
                ['id' => 'order', 'title' => '🛒 طلب'],
                ['id' => 'location', 'title' => '📍 الموقع']
            ];
            break;
            
        case 'ecommerce':
            $buttons = [
                ['id' => 'products', 'title' => '🛍️ المنتجات'],
                ['id' => 'cart', 'title' => '🛒 السلة'],
                ['id' => 'orders', 'title' => '📦 طلباتي']
            ];
            break;
            
        case 'support':
            $buttons = [
                ['id' => 'faq', 'title' => '❓ الأسئلة الشائعة'],
                ['id' => 'ticket', 'title' => '🎫 تذكرة دعم'],
                ['id' => 'call', 'title' => '📞 اتصال']
            ];
            break;
    }
    
    if (!empty($buttons)) {
        return $whatsappAPI->sendButtonMessage(
            $phoneNumber,
            'كيف يمكنني مساعدتك؟',
            $buttons,
            'مرحباً بك',
            'اختر الخيار المناسب'
        );
    }
    
    return false;
}

echo "\n💡 نصائح للاستخدام الأمثل:\n";
echo "• استخدم نصوص واضحة ومختصرة للأزرار\n";
echo "• رتب الأزرار حسب الأهمية\n";
echo "• استخدم الرموز التعبيرية لجعل الأزرار أكثر جاذبية\n";
echo "• اختبر الأزرار قبل إرسالها للعملاء\n";
echo "• راقب إحصائيات النقر لتحسين التفاعل\n";
?>
