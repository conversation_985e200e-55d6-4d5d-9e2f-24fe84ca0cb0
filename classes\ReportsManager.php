<?php
/**
 * فئة إدارة التقارير
 * Reports Manager Class
 */

class ReportsManager {
    private $database;
    
    public function __construct($database) {
        $this->database = $database;
    }
    
    /**
     * تقرير الرسائل العام
     * General Messages Report
     */
    public function getMessagesReport($startDate = null, $endDate = null, $contactId = null) {
        $whereClause = "1=1";
        $params = [];
        
        if ($startDate) {
            $whereClause .= " AND m.created_at >= ?";
            $params[] = $startDate . ' 00:00:00';
        }
        
        if ($endDate) {
            $whereClause .= " AND m.created_at <= ?";
            $params[] = $endDate . ' 23:59:59';
        }
        
        if ($contactId) {
            $whereClause .= " AND m.contact_id = ?";
            $params[] = $contactId;
        }
        
        // الإحصائيات العامة
        $stats = $this->database->fetchOne("
            SELECT 
                COUNT(*) as total_messages,
                SUM(CASE WHEN direction = 'outgoing' THEN 1 ELSE 0 END) as sent_messages,
                SUM(CASE WHEN direction = 'incoming' THEN 1 ELSE 0 END) as received_messages,
                SUM(CASE WHEN is_automated = 1 THEN 1 ELSE 0 END) as automated_messages,
                SUM(CASE WHEN status = 'delivered' THEN 1 ELSE 0 END) as delivered_messages,
                SUM(CASE WHEN status = 'read' THEN 1 ELSE 0 END) as read_messages,
                SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed_messages
            FROM messages m 
            WHERE {$whereClause}
        ", $params);
        
        // توزيع أنواع الرسائل
        $messageTypes = $this->database->fetchAll("
            SELECT 
                message_type,
                COUNT(*) as count,
                ROUND((COUNT(*) * 100.0 / (SELECT COUNT(*) FROM messages WHERE {$whereClause})), 2) as percentage
            FROM messages m
            WHERE {$whereClause}
            GROUP BY message_type
            ORDER BY count DESC
        ", $params);
        
        // الرسائل حسب الساعة
        $hourlyStats = $this->database->fetchAll("
            SELECT 
                HOUR(created_at) as hour,
                COUNT(*) as count
            FROM messages m
            WHERE {$whereClause}
            GROUP BY HOUR(created_at)
            ORDER BY hour
        ", $params);
        
        // الرسائل حسب اليوم
        $dailyStats = $this->database->fetchAll("
            SELECT 
                DATE(created_at) as date,
                COUNT(*) as total,
                SUM(CASE WHEN direction = 'outgoing' THEN 1 ELSE 0 END) as sent,
                SUM(CASE WHEN direction = 'incoming' THEN 1 ELSE 0 END) as received
            FROM messages m
            WHERE {$whereClause}
            GROUP BY DATE(created_at)
            ORDER BY date DESC
            LIMIT 30
        ", $params);
        
        return [
            'stats' => $stats,
            'message_types' => $messageTypes,
            'hourly_stats' => $hourlyStats,
            'daily_stats' => $dailyStats
        ];
    }
    
    /**
     * تقرير جهات الاتصال
     * Contacts Report
     */
    public function getContactsReport($startDate = null, $endDate = null) {
        $whereClause = "1=1";
        $params = [];
        
        if ($startDate) {
            $whereClause .= " AND c.created_at >= ?";
            $params[] = $startDate . ' 00:00:00';
        }
        
        if ($endDate) {
            $whereClause .= " AND c.created_at <= ?";
            $params[] = $endDate . ' 23:59:59';
        }
        
        // إحصائيات جهات الاتصال
        $stats = $this->database->fetchOne("
            SELECT 
                COUNT(*) as total_contacts,
                SUM(CASE WHEN last_message_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 ELSE 0 END) as active_contacts,
                SUM(CASE WHEN last_message_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 ELSE 0 END) as monthly_active,
                SUM(CASE WHEN is_blocked = 1 THEN 1 ELSE 0 END) as blocked_contacts
            FROM contacts c
            WHERE {$whereClause}
        ", $params);
        
        // أكثر جهات الاتصال تفاعلاً
        $topContacts = $this->database->fetchAll("
            SELECT 
                c.name,
                c.phone_number,
                COUNT(m.id) as message_count,
                MAX(m.created_at) as last_message
            FROM contacts c
            LEFT JOIN messages m ON c.id = m.contact_id
            WHERE {$whereClause}
            GROUP BY c.id
            ORDER BY message_count DESC
            LIMIT 20
        ", $params);
        
        // جهات الاتصال الجديدة حسب التاريخ
        $newContactsDaily = $this->database->fetchAll("
            SELECT 
                DATE(created_at) as date,
                COUNT(*) as count
            FROM contacts c
            WHERE {$whereClause}
            GROUP BY DATE(created_at)
            ORDER BY date DESC
            LIMIT 30
        ", $params);
        
        return [
            'stats' => $stats,
            'top_contacts' => $topContacts,
            'new_contacts_daily' => $newContactsDaily
        ];
    }
    
    /**
     * تقرير الردود التلقائية
     * Auto Replies Report
     */
    public function getAutoRepliesReport($startDate = null, $endDate = null) {
        $whereClause = "1=1";
        $params = [];
        
        if ($startDate || $endDate) {
            $whereClause .= " AND ar.created_at >= ? AND ar.created_at <= ?";
            $params[] = $startDate ? $startDate . ' 00:00:00' : '1970-01-01';
            $params[] = $endDate ? $endDate . ' 23:59:59' : date('Y-m-d 23:59:59');
        }
        
        // إحصائيات الردود التلقائية
        $stats = $this->database->fetchOne("
            SELECT 
                COUNT(*) as total_auto_replies,
                SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active_replies,
                SUM(usage_count) as total_usage
            FROM auto_replies ar
            WHERE {$whereClause}
        ", $params);
        
        // أكثر الردود التلقائية استخداماً
        $topAutoReplies = $this->database->fetchAll("
            SELECT 
                name,
                keywords,
                usage_count,
                is_active,
                created_at
            FROM auto_replies ar
            WHERE {$whereClause}
            ORDER BY usage_count DESC
            LIMIT 20
        ", $params);
        
        // استخدام الردود التلقائية حسب التاريخ
        $autoReplyUsage = $this->database->fetchAll("
            SELECT 
                DATE(m.created_at) as date,
                COUNT(*) as count
            FROM messages m
            WHERE m.is_automated = 1 
            AND m.created_at >= ? 
            AND m.created_at <= ?
            GROUP BY DATE(m.created_at)
            ORDER BY date DESC
            LIMIT 30
        ", [
            $startDate ? $startDate . ' 00:00:00' : date('Y-m-d', strtotime('-30 days')) . ' 00:00:00',
            $endDate ? $endDate . ' 23:59:59' : date('Y-m-d 23:59:59')
        ]);
        
        return [
            'stats' => $stats,
            'top_auto_replies' => $topAutoReplies,
            'usage_daily' => $autoReplyUsage
        ];
    }
    
    /**
     * تقرير الأداء
     * Performance Report
     */
    public function getPerformanceReport($startDate = null, $endDate = null) {
        $whereClause = "1=1";
        $params = [];
        
        if ($startDate) {
            $whereClause .= " AND created_at >= ?";
            $params[] = $startDate . ' 00:00:00';
        }
        
        if ($endDate) {
            $whereClause .= " AND created_at <= ?";
            $params[] = $endDate . ' 23:59:59';
        }
        
        // معدل الاستجابة
        $responseRate = $this->database->fetchOne("
            SELECT 
                AVG(CASE WHEN status IN ('delivered', 'read') THEN 1 ELSE 0 END) * 100 as delivery_rate,
                AVG(CASE WHEN status = 'read' THEN 1 ELSE 0 END) * 100 as read_rate,
                AVG(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) * 100 as failure_rate
            FROM messages 
            WHERE direction = 'outgoing' AND {$whereClause}
        ", $params);
        
        // متوسط وقت الاستجابة
        $responseTime = $this->database->fetchOne("
            SELECT 
                AVG(TIMESTAMPDIFF(MINUTE, m1.created_at, m2.created_at)) as avg_response_time_minutes
            FROM messages m1
            JOIN messages m2 ON m1.contact_id = m2.contact_id
            WHERE m1.direction = 'incoming' 
            AND m2.direction = 'outgoing'
            AND m2.created_at > m1.created_at
            AND m2.created_at = (
                SELECT MIN(created_at) 
                FROM messages 
                WHERE contact_id = m1.contact_id 
                AND direction = 'outgoing' 
                AND created_at > m1.created_at
            )
            AND m1.{$whereClause}
        ", $params);
        
        // الذروة اليومية
        $peakHours = $this->database->fetchAll("
            SELECT 
                HOUR(created_at) as hour,
                COUNT(*) as message_count
            FROM messages 
            WHERE {$whereClause}
            GROUP BY HOUR(created_at)
            ORDER BY message_count DESC
            LIMIT 5
        ", $params);
        
        return [
            'response_rate' => $responseRate,
            'response_time' => $responseTime,
            'peak_hours' => $peakHours
        ];
    }
    
    /**
     * تقرير الحملات التسويقية
     * Campaigns Report
     */
    public function getCampaignsReport($startDate = null, $endDate = null) {
        $whereClause = "1=1";
        $params = [];
        
        if ($startDate) {
            $whereClause .= " AND created_at >= ?";
            $params[] = $startDate . ' 00:00:00';
        }
        
        if ($endDate) {
            $whereClause .= " AND created_at <= ?";
            $params[] = $endDate . ' 23:59:59';
        }
        
        // إحصائيات الحملات
        $stats = $this->database->fetchOne("
            SELECT 
                COUNT(*) as total_campaigns,
                SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_campaigns,
                SUM(CASE WHEN status = 'running' THEN 1 ELSE 0 END) as active_campaigns,
                SUM(total_recipients) as total_recipients,
                SUM(sent_count) as total_sent,
                SUM(delivered_count) as total_delivered,
                SUM(read_count) as total_read,
                AVG(CASE WHEN total_recipients > 0 THEN (delivered_count * 100.0 / total_recipients) ELSE 0 END) as avg_delivery_rate
            FROM campaigns 
            WHERE {$whereClause}
        ", $params);
        
        // أفضل الحملات أداءً
        $topCampaigns = $this->database->fetchAll("
            SELECT 
                name,
                total_recipients,
                sent_count,
                delivered_count,
                read_count,
                CASE WHEN total_recipients > 0 THEN ROUND((delivered_count * 100.0 / total_recipients), 2) ELSE 0 END as delivery_rate,
                CASE WHEN delivered_count > 0 THEN ROUND((read_count * 100.0 / delivered_count), 2) ELSE 0 END as read_rate,
                created_at
            FROM campaigns 
            WHERE {$whereClause} AND status = 'completed'
            ORDER BY delivery_rate DESC, read_rate DESC
            LIMIT 10
        ", $params);
        
        return [
            'stats' => $stats,
            'top_campaigns' => $topCampaigns
        ];
    }
    
    /**
     * تصدير التقرير إلى CSV
     * Export report to CSV
     */
    public function exportToCSV($reportData, $filename) {
        $filepath = REPORTS_PATH . $filename . '_' . date('Y-m-d_H-i-s') . '.csv';
        
        $file = fopen($filepath, 'w');
        
        // إضافة BOM للدعم العربي
        fwrite($file, "\xEF\xBB\xBF");
        
        // كتابة البيانات
        foreach ($reportData as $row) {
            fputcsv($file, $row);
        }
        
        fclose($file);
        
        return $filepath;
    }
    
    /**
     * تصدير التقرير إلى PDF
     * Export report to PDF
     */
    public function exportToPDF($reportData, $title, $filename) {
        // يتطلب مكتبة TCPDF أو مشابهة
        // هذا مثال أساسي
        
        $html = "<html><head><meta charset='UTF-8'><title>{$title}</title></head><body>";
        $html .= "<h1>{$title}</h1>";
        $html .= "<table border='1' cellpadding='5'>";
        
        foreach ($reportData as $row) {
            $html .= "<tr>";
            foreach ($row as $cell) {
                $html .= "<td>" . htmlspecialchars($cell) . "</td>";
            }
            $html .= "</tr>";
        }
        
        $html .= "</table></body></html>";
        
        $filepath = REPORTS_PATH . $filename . '_' . date('Y-m-d_H-i-s') . '.html';
        file_put_contents($filepath, $html);
        
        return $filepath;
    }
}
?>
