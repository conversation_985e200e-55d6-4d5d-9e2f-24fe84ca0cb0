# نظام أتمتة WhatsApp Business

نظام شامل ومتطور لأتمتة WhatsApp Business باستخدام PHP و MySQL مع واجهة إدارة متقدمة ونظام تقارير مفصل.

## 🌟 المميزات الرئيسية

### 📱 إدارة الرسائل
- **إرسال واستقبال الرسائل** - دعم جميع أنواع الرسائل (نص، صور، مستندات، صوت، فيديو)
- **الردود التلقائية الذكية** - ردود مخصصة حسب الكلمات المفتاحية والسياق
- **جدولة الرسائل** - إرسال رسائل في أوقات محددة مع إمكانية التكرار
- **قوالب الرسائل** - إنشاء وإدارة قوالب جاهزة للاستخدام

### 👥 إدارة جهات الاتصال
- **قاعدة بيانات شاملة** - حفظ وتنظيم معلومات العملاء
- **تصنيف العملاء** - تجميع العملاء حسب الفئات والعلامات
- **تتبع التفاعل** - مراقبة نشاط وتفاعل كل عميل
- **الاستيراد والتصدير** - دعم ملفات CSV و Excel

### 🤖 الأتمتة المتقدمة
- **تحليل المشاعر** - فهم مشاعر العملاء والرد المناسب
- **تصنيف الاستفسارات** - تحديد نوع الاستفسار تلقائياً
- **المتابعة التلقائية** - رسائل متابعة مجدولة حسب السيناريو
- **نظام التقييم** - طلب تقييم العملاء وتحليل الردود

### 📊 التقارير والإحصائيات
- **تقارير شاملة** - إحصائيات مفصلة عن الرسائل والعملاء
- **تحليلات الأداء** - معدلات الاستجابة والتفاعل
- **الرسوم البيانية** - عرض البيانات بصرياً
- **التصدير** - تصدير التقارير بصيغ مختلفة (CSV, PDF, Excel)

### 🚀 الحملات التسويقية
- **إنشاء الحملات** - حملات تسويقية مستهدفة
- **الاستهداف الذكي** - اختيار المستلمين حسب معايير محددة
- **تتبع النتائج** - مراقبة أداء الحملات في الوقت الفعلي
- **التحليل المتقدم** - تحليل فعالية الحملات

## 🛠️ المتطلبات التقنية

### متطلبات الخادم
- **PHP 8.0+** مع الإضافات التالية:
  - PDO MySQL
  - cURL
  - JSON
  - mbstring
  - OpenSSL
- **MySQL 8.0+** أو **MariaDB 10.4+**
- **Apache 2.4+** أو **Nginx 1.18+**
- **SSL Certificate** (مطلوب لـ WhatsApp Webhook)

### متطلبات WhatsApp Business
- **حساب WhatsApp Business** مُفعل
- **Facebook Business Manager** مع صلاحيات الإدارة
- **WhatsApp Business API** مُعتمد من Meta
- **Webhook URL** آمن مع HTTPS

## 📦 التثبيت والإعداد

### 1. تحميل الملفات
```bash
git clone https://github.com/your-repo/whatsapp-automation.git
cd whatsapp-automation
```

### 2. إعداد قاعدة البيانات
```bash
# إنشاء قاعدة البيانات
mysql -u root -p -e "CREATE DATABASE whatsapp_automation CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# استيراد الهيكل
mysql -u root -p whatsapp_automation < database/whatsapp_automation.sql
```

### 3. تكوين الإعدادات
```bash
# نسخ ملف الإعدادات
cp .env.example .env

# تعديل الإعدادات
nano .env
```

### 4. إعداد صلاحيات المجلدات
```bash
chmod 755 -R admin/
chmod 777 -R logs/
chmod 777 -R uploads/
chmod 777 -R reports/
chmod 777 -R backups/
```

### 5. إعداد Cron Jobs
```bash
# إضافة المهام المجدولة
crontab -e

# إضافة السطر التالي:
*/5 * * * * /usr/bin/php /path/to/your/project/cron/scheduler.php
```

## ⚙️ إعداد WhatsApp Business API

### 1. إنشاء تطبيق Facebook
1. اذهب إلى [Facebook Developers](https://developers.facebook.com/)
2. أنشئ تطبيق جديد من نوع "Business"
3. أضف منتج "WhatsApp Business Platform"

### 2. الحصول على المفاتيح
```env
WHATSAPP_ACCESS_TOKEN=your_permanent_access_token
WHATSAPP_PHONE_NUMBER_ID=your_phone_number_id
WHATSAPP_WEBHOOK_VERIFY_TOKEN=your_custom_verify_token
```

### 3. إعداد Webhook
- **URL**: `https://yourdomain.com/webhook.php`
- **Verify Token**: نفس القيمة في `.env`
- **Fields**: `messages`, `message_deliveries`, `message_reads`

## 🎯 الاستخدام السريع

### تسجيل الدخول الأول
- **الرابط**: `https://yourdomain.com/admin/`
- **اسم المستخدم**: `admin`
- **كلمة المرور**: `admin123` (يُنصح بتغييرها فوراً)

### إعداد الردود التلقائية
1. اذهب إلى "الردود التلقائية"
2. انقر "إضافة رد جديد"
3. أدخل الكلمات المفتاحية والرد المناسب
4. حدد الشروط والأولوية

### إنشاء حملة تسويقية
1. اذهب إلى "الحملات التسويقية"
2. انقر "إنشاء حملة جديدة"
3. اكتب محتوى الرسالة
4. حدد المستلمين المستهدفين
5. جدول وقت الإرسال

## 📈 مراقبة الأداء

### لوحة التحكم
- **إحصائيات فورية** - عدد الرسائل والعملاء النشطين
- **الرسوم البيانية** - تمثيل بصري للبيانات
- **التنبيهات** - إشعارات المشاكل والأحداث المهمة

### التقارير المتقدمة
- **تقرير الرسائل** - تحليل شامل لحركة الرسائل
- **تقرير العملاء** - إحصائيات التفاعل والنشاط
- **تقرير الأداء** - معدلات النجاح والاستجابة
- **تقرير الحملات** - فعالية الحملات التسويقية

## 🔧 الصيانة والنسخ الاحتياطي

### النسخ الاحتياطي التلقائي
- **يومياً في الساعة 2:00 صباحاً**
- **الاحتفاظ بـ 30 نسخة**
- **ضغط تلقائي لتوفير المساحة**

### تنظيف البيانات
- **الرسائل القديمة** - حذف بعد 6 أشهر
- **سجلات النشاط** - حذف بعد 3 أشهر
- **ملفات السجلات** - حذف بعد 30 يوم

## 🛡️ الأمان والحماية

### حماية البيانات
- **تشفير كلمات المرور** - باستخدام bcrypt
- **حماية من SQL Injection** - استخدام Prepared Statements
- **حماية من XSS** - تنظيف المدخلات
- **حماية CSRF** - رموز الحماية

### مراقبة الأمان
- **سجلات النشاط** - تتبع جميع العمليات
- **تنبيهات الأمان** - إشعارات المحاولات المشبوهة
- **جلسات آمنة** - انتهاء صلاحية تلقائي

## 🆘 استكشاف الأخطاء

### مشاكل شائعة

#### لا تصل الرسائل
1. تحقق من صحة Access Token
2. تأكد من تفعيل Webhook
3. راجع سجلات الأخطاء في `logs/`

#### مشاكل قاعدة البيانات
1. تحقق من إعدادات الاتصال في `.env`
2. تأكد من صلاحيات المستخدم
3. راجع سجلات MySQL

#### مشاكل الأداء
1. تحقق من موارد الخادم
2. راجع إعدادات PHP (memory_limit, max_execution_time)
3. تحسين استعلامات قاعدة البيانات

### ملفات السجلات
- `logs/whatsapp_api_*.log` - سجلات API
- `logs/webhook_*.log` - سجلات Webhook
- `logs/scheduler_*.log` - سجلات المجدول
- `logs/error_*.log` - سجلات الأخطاء العامة

## 📞 الدعم الفني

### التواصل
- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: +966-XX-XXX-XXXX
- **ساعات الدعم**: من 9 صباحاً إلى 6 مساءً

### الموارد المفيدة
- [دليل WhatsApp Business API](https://developers.facebook.com/docs/whatsapp)
- [وثائق PHP](https://www.php.net/docs.php)
- [دليل MySQL](https://dev.mysql.com/doc/)

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 🤝 المساهمة

نرحب بمساهماتكم! يرجى قراءة [دليل المساهمة](CONTRIBUTING.md) قبل إرسال Pull Request.

---

**تم تطوير هذا النظام بعناية لخدمة الشركات والمؤسسات التي تسعى لتحسين تواصلها مع العملاء عبر WhatsApp Business.**
