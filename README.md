# WhatsApp Business Automation

نظام أتمتة شامل لـ WhatsApp Business يتيح لك إدارة الرسائل والردود التلقائية وجدولة الرسائل وإدارة جهات الاتصال.

## المميزات

- 🤖 **ردود تلقائية ذكية** - ردود مخصصة حسب الكلمات المفتاحية
- ⏰ **جدولة الرسائل** - إرسال رسائل في أوقات محددة
- 👥 **إدارة جهات الاتصال** - تنظيم وإدارة قوائم العملاء
- 📊 **تقارير مفصلة** - إحصائيات الرسائل والتفاعل
- 🕐 **ساعات العمل** - ردود مختلفة حسب أوقات العمل
- 📱 **واجهة ويب** - لوحة تحكم سهلة الاستخدام
- 🔒 **آمان عالي** - حماية البيانات والخصوصية

## التثبيت السريع

1. **استنساخ المشروع**
```bash
git clone <repository-url>
cd whatsapp-business-automation
```

2. **تثبيت المتطلبات**
```bash
npm install
```

3. **إعداد التكوين**
```bash
cp .env.example .env
# قم بتعديل ملف .env حسب احتياجاتك
```

4. **تشغيل النظام**
```bash
npm start
```

5. **مسح رمز QR**
   - سيظهر رمز QR في الطرفية
   - امسحه بتطبيق WhatsApp على هاتفك
   - انتظر حتى يتم الاتصال بنجاح

## الاستخدام

### الردود التلقائية
النظام يدعم ردود تلقائية ذكية حسب:
- الكلمات المفتاحية
- ساعات العمل
- نوع الرسالة (نص، صورة، ملف)

### جدولة الرسائل
```javascript
// مثال على جدولة رسالة
scheduler.scheduleMessage({
  to: '<EMAIL>',
  message: 'رسالة مجدولة',
  time: '2024-01-01 10:00:00'
});
```

### إدارة جهات الاتصال
- إضافة وحذف جهات الاتصال
- تصنيف العملاء
- تصدير واستيراد قوائم الاتصال

## الهيكل

```
src/
├── index.js              # نقطة البداية
├── client/               # عميل WhatsApp
├── automation/           # منطق الأتمتة
├── scheduler/            # جدولة الرسائل
├── contacts/             # إدارة جهات الاتصال
├── config/               # إعدادات النظام
├── utils/                # أدوات مساعدة
└── web/                  # واجهة الويب
```

## المتطلبات

- Node.js 16+ 
- npm أو yarn
- اتصال إنترنت مستقر
- حساب WhatsApp نشط

## الدعم

للمساعدة والدعم الفني، يرجى فتح issue في المستودع أو التواصل معنا.

## الترخيص

MIT License - راجع ملف LICENSE للتفاصيل.
